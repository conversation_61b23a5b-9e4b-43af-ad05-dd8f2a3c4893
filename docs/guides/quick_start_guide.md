# LLIE项目快速实践指南

本指南将带您完成一个完整的低光图像增强研究闭环，从数据准备到模型优化，让您快速上手LLIE项目。

## 🎯 学习目标

通过本指南，您将学会：
- 准备和组织LOL数据集
- 训练DMFourLLIE模型
- 对模型进行推理测试
- 使用多种指标评估模型性能
- 修改网络架构组件
- 基于评估结果进行迭代优化

## 📋 前置要求

### 环境准备
```bash
# 1. 激活conda环境
conda activate llie

# 2. 验证环境
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
```

### 硬件要求
- **GPU**: 推荐NVIDIA GPU (4GB+ 显存)
- **内存**: 至少8GB RAM
- **存储**: 至少10GB可用空间

## 🚀 完整实践流程

### 第一步：数据准备和训练

#### 1.1 下载和组织LOL数据集

```bash
# 创建数据目录结构
mkdir -p data/LOLv2/Real_captured/{Train,Test}/{Low,Normal}

# 目录结构应该如下：
# data/LOLv2/Real_captured/
# ├── Train/
# │   ├── Low/      # 训练用低光图像 (485张)
# │   └── Normal/   # 训练用正常光照图像 (485张)
# └── Test/
#     ├── Low/      # 测试用低光图像 (100张)
#     └── Normal/   # 测试用正常光照图像 (100张)
```

**数据集下载地址：**
- LOLv1: https://daooshee.github.io/BMVC2018website/
- LOLv2: https://github.com/flyywh/CVPR-2020-Semi-Low-Light

#### 1.2 验证数据集

```bash
# 检查数据集完整性
python -c "
import os
from pathlib import Path

data_root = Path('data/LOLv2/Real_captured')
for split in ['Train', 'Test']:
    for light in ['Low', 'Normal']:
        path = data_root / split / light
        if path.exists():
            count = len(list(path.glob('*.png'))) + len(list(path.glob('*.jpg')))
            print(f'{split}/{light}: {count} 张图像')
        else:
            print(f'❌ 缺少目录: {path}')
"
```

#### 1.3 开始初始训练

```bash
# 使用默认配置开始训练
python run.py

# 或者使用自定义配置
python run.py \
  name=my_first_experiment \
  trainer.max_epochs=50 \
  trainer.batch_size=8 \
  wandb.tags=[first_run,baseline]
```

**训练过程监控：**
- 终端会显示Rich进度条和实时指标
- 如果配置了W&B，可以在浏览器中查看详细日志
- 检查点会自动保存到 `outputs/` 目录

### 第二步：模型测试

#### 2.1 单张图像推理

```bash
# 对单张图像进行推理
python run.py task=inference \
  inference.checkpoint_path=outputs/checkpoints/best_model.pth \
  inference.input_path=data/LOLv2/Real_captured/Test/Low/1.png \
  inference.output_path=outputs/enhanced_image.png
```

#### 2.2 批量图像处理

```bash
# 处理整个测试集
python run.py task=inference \
  inference.checkpoint_path=outputs/checkpoints/best_model.pth \
  inference.input_path=data/LOLv2/Real_captured/Test/Low \
  inference.output_path=outputs/enhanced_results \
  inference.batch_size=4
```

#### 2.3 结果可视化

```python
# 创建对比图像的简单脚本
import matplotlib.pyplot as plt
from PIL import Image
import numpy as np

def compare_images(low_path, enhanced_path, normal_path):
    """对比低光、增强和正常光照图像"""
    low = Image.open(low_path)
    enhanced = Image.open(enhanced_path)
    normal = Image.open(normal_path)
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    axes[0].imshow(low)
    axes[0].set_title('低光图像')
    axes[0].axis('off')
    
    axes[1].imshow(enhanced)
    axes[1].set_title('增强结果')
    axes[1].axis('off')
    
    axes[2].imshow(normal)
    axes[2].set_title('参考图像')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig('comparison.png', dpi=150, bbox_inches='tight')
    plt.show()

# 使用示例
compare_images(
    'data/LOLv2/Real_captured/Test/Low/1.png',
    'outputs/enhanced_results/1.png',
    'data/LOLv2/Real_captured/Test/Normal/1.png'
)
```

### 第三步：结果评估

#### 3.1 定量评估

```bash
# 在测试集上进行全面评估
python run.py task=evaluate \
  evaluation.checkpoint_path=outputs/checkpoints/best_model.pth \
  evaluation.test_data_path=data/LOLv2/Real_captured/Test \
  evaluation.save_results=true
```

**评估指标说明：**
- **PSNR (峰值信噪比)**: 越高越好，通常 > 20dB 为良好
- **SSIM (结构相似性)**: 0-1之间，越接近1越好
- **MAE (平均绝对误差)**: 越小越好
- **LPIPS (感知损失)**: 越小越好，衡量感知质量

#### 3.2 查看评估报告

```python
# 分析评估结果
import json
import pandas as pd

# 读取评估结果
with open('outputs/evaluation_results.json', 'r') as f:
    results = json.load(f)

# 创建结果表格
metrics_df = pd.DataFrame([results['metrics']])
print("=== 模型性能评估 ===")
print(metrics_df.round(4))

# 分析各项指标
print(f"\n=== 性能分析 ===")
print(f"PSNR: {results['metrics']['psnr']:.2f} dB")
print(f"SSIM: {results['metrics']['ssim']:.4f}")
print(f"MAE: {results['metrics']['mae']:.4f}")

# 性能判断
if results['metrics']['psnr'] > 20:
    print("✅ PSNR表现良好")
else:
    print("⚠️ PSNR需要改进")

if results['metrics']['ssim'] > 0.8:
    print("✅ SSIM表现良好")
else:
    print("⚠️ SSIM需要改进")
```

### 第四步：网络架构修改

#### 4.1 理解注册表系统

LLIE项目使用注册表模式，让您可以轻松替换和修改网络组件：

```python
# 查看已注册的组件
from src.llie.utils.registry import MODELS, LOSSES

print("已注册的模型:", list(MODELS.module_dict.keys()))
print("已注册的损失函数:", list(LOSSES.module_dict.keys()))
```

#### 4.2 修改模型配置

**实验1：调整模型大小**
```bash
# 创建轻量级模型配置
cp configs/model/dmfourllie.yaml configs/model/dmfourllie_light.yaml
```

编辑 `configs/model/dmfourllie_light.yaml`：
```yaml
# 轻量级配置
type: DMFourLLIE

architecture:
  y_nf: 8          # 减少亮度处理通道
  f_nf: 8          # 减少傅里叶处理通道
  s_nf: 16         # 减少空间处理通道
  num_blocks: 4    # 减少FFC块数量
  input_channels: 3

components:
  luminance_map:
    type: LuminanceMap
    depth: [1, 1, 1, 1]
    base_channel: ${model.architecture.y_nf}  # 注意：使用正确的插值引用

  fourier_stage:
    type: FirstProcessModel
    nf: ${model.architecture.f_nf}

  multi_stage:
    type: SecondProcessModel
    nf: ${model.architecture.s_nf}
    num_blocks: ${model.architecture.num_blocks}
    input_channels: ${model.architecture.input_channels}
```

**实验2：增强模型配置**
```yaml
# 增强版配置
type: DMFourLLIE

architecture:
  y_nf: 32         # 增加亮度处理通道
  f_nf: 32         # 增加傅里叶处理通道
  s_nf: 64         # 增加空间处理通道
  num_blocks: 8    # 增加FFC块数量
  input_channels: 3

components:
  luminance_map:
    type: LuminanceMap
    depth: [1, 1, 1, 1]
    base_channel: ${model.architecture.y_nf}  # 注意：使用正确的插值引用

  fourier_stage:
    type: FirstProcessModel
    nf: ${model.architecture.f_nf}

  multi_stage:
    type: SecondProcessModel
    nf: ${model.architecture.s_nf}
    num_blocks: ${model.architecture.num_blocks}
    input_channels: ${model.architecture.input_channels}
```

#### 4.3 创建新的损失函数

```python
# 在 src/llie/losses/custom_losses.py 中创建新损失函数
from ..utils.registry import register_loss
import torch.nn as nn

@register_loss("MyCustomLoss")
class MyCustomLoss(nn.Module):
    """自定义损失函数示例"""
    
    def __init__(self, alpha=1.0, beta=0.1):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.l1_loss = nn.L1Loss()
        self.mse_loss = nn.MSELoss()
    
    def forward(self, pred, target):
        l1 = self.l1_loss(pred, target)
        mse = self.mse_loss(pred, target)
        return self.alpha * l1 + self.beta * mse
```

#### 4.4 测试不同配置

```bash
# 测试轻量级模型
python run.py model=dmfourllie_light \
  name=light_model_experiment \
  trainer.max_epochs=20

# 测试增强模型
python run.py model=dmfourllie_enhanced \
  name=enhanced_model_experiment \
  trainer.max_epochs=20

# 测试自定义损失函数
python run.py trainer.loss.type=MyCustomLoss \
  trainer.loss.alpha=1.0 \
  trainer.loss.beta=0.2 \
  name=custom_loss_experiment
```

### 第五步：迭代优化训练

#### 5.1 分析实验结果

```python
# 比较不同实验的结果
import wandb
import matplotlib.pyplot as plt

# 如果使用W&B，可以下载实验数据进行对比
# 或者从本地日志文件中提取数据

experiments = {
    'baseline': {'psnr': 22.5, 'ssim': 0.82, 'mae': 0.045},
    'light_model': {'psnr': 21.8, 'ssim': 0.80, 'mae': 0.048},
    'enhanced_model': {'psnr': 23.2, 'ssim': 0.85, 'mae': 0.042},
    'custom_loss': {'psnr': 22.8, 'ssim': 0.83, 'mae': 0.043}
}

# 可视化对比
metrics = ['psnr', 'ssim', 'mae']
fig, axes = plt.subplots(1, 3, figsize=(15, 5))

for i, metric in enumerate(metrics):
    values = [exp[metric] for exp in experiments.values()]
    axes[i].bar(experiments.keys(), values)
    axes[i].set_title(f'{metric.upper()} 对比')
    axes[i].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.savefig('experiment_comparison.png')
plt.show()
```

#### 5.2 基于结果进行优化

**根据评估结果选择最佳配置：**

1. **如果PSNR较低**：
   - 增加模型容量（更多通道、更多块）
   - 调整损失函数权重
   - 增加训练轮数

2. **如果SSIM较低**：
   - 添加SSIM损失项
   - 调整数据增强策略
   - 检查数据质量

3. **如果推理速度慢**：
   - 使用轻量级配置
   - 减少FFC块数量
   - 优化输入尺寸

#### 5.3 最终优化训练

```bash
# 基于最佳配置进行最终训练
python run.py \
  model=dmfourllie_enhanced \
  trainer.loss.type=CombinedLoss \
  trainer.loss.pixel_weight=1.0 \
  trainer.loss.ssim_weight=0.2 \
  trainer.max_epochs=100 \
  trainer.batch_size=16 \
  trainer.use_amp=true \
  name=final_optimized_model \
  wandb.tags=[final,optimized,best_config]
```

## 🎯 快速检查清单

### 训练前检查
- [ ] 数据集已正确下载和组织
- [ ] 环境配置正确，GPU可用
- [ ] 配置文件参数合理
- [ ] W&B配置正确（如果使用）

### 训练中监控
- [ ] 损失函数正常下降
- [ ] 验证指标稳步提升
- [ ] 没有过拟合现象
- [ ] GPU利用率合理

### 评估后分析
- [ ] PSNR > 20dB
- [ ] SSIM > 0.8
- [ ] 视觉效果满意
- [ ] 推理速度可接受

## 🔧 常见问题快速解决

### 训练问题
```bash
# 显存不足
python run.py trainer.batch_size=4 trainer.use_amp=true

# 训练太慢
python run.py num_workers=8 dataset.cache_images=true

# 过拟合
python run.py trainer.early_stopping.patience=10
```

### 推理问题
```bash
# 推理结果不理想
python run.py task=inference inference.tta=true  # 测试时增强

# 批量处理内存不足
python run.py task=inference inference.batch_size=1
```

## 📚 进阶学习

完成快速实践后，建议深入学习：
- [数据处理完整指南](data_processing_guide.md)
- [模型训练详细教程](training_tutorial.md)
- [网络架构修改实战](architecture_modification.md)
- [模型评估方法论](evaluation_methodology.md)

## 🎉 恭喜！

您已经完成了LLIE项目的完整实践流程！现在您应该能够：
- 独立训练低光图像增强模型
- 评估和优化模型性能
- 修改网络架构进行实验
- 进行完整的研究闭环

继续探索更高级的功能和技术，祝您在低光图像增强研究中取得成功！
