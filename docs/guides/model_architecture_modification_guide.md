# LLIE项目模型架构修改完整教程

> **版本**: v1.0  
> **更新时间**: 2025-01-31  
> **适用范围**: LLIE低光图像增强项目的网络结构修改实验

## 📋 目录

1. [总体架构说明](#1-总体架构说明)
2. [具体修改场景详细步骤](#2-具体修改场景详细步骤)
3. [文件修改指南](#3-文件修改指南)
4. [验证和测试流程](#4-验证和测试流程)
5. [最佳实践建议](#5-最佳实践建议)

---

## 1. 总体架构说明

### 1.1 项目模型架构组织方式

LLIE项目采用**模块化设计**和**注册表模式**，具有以下特点：

```
src/llie/models/
├── __init__.py              # 模型包初始化，导入所有模型
├── base_model.py           # 基础模型抽象类
├── dmfourllie.py          # 主模型：DMFourLLIE架构
└── components/            # 可复用的模型组件
    ├── __init__.py        # 组件包初始化
    ├── common.py          # 通用组件（注意力、残差块等）
    ├── luminance_map.py   # 亮度图处理组件
    ├── fourier_blocks.py  # 傅里叶变换相关组件
    └── ffc_blocks.py      # FFC（快速傅里叶卷积）组件
```

### 1.2 DMFourLLIE架构的四个处理级别

```python
# DMFourLLIE的四级处理流程
class DMFourLLIE(BaseArchitecture):
    """
    1. 亮度图处理 (Luminance Map Processing)
       - 提取图像亮度信息，为后续处理提供照明指导
       
    2. 傅里叶域增强 (Fourier Domain Enhancement)  
       - 在频域进行全局特征处理，利用FFT进行跨模态特征融合
       
    3. 空间-频率双重处理 (Spatial-Frequency Dual Processing)
       - 结合空间域和频域特征，使用FFC块进行特征融合
       
    4. 最终重建 (Final Reconstruction)
       - 生成增强后的图像，保持图像细节和颜色一致性
    """
```

### 1.3 配置文件层次结构

```yaml
configs/
├── config.yaml                 # 主配置文件，组合所有组件
├── model/
│   └── dmfourllie.yaml        # 模型架构参数配置
├── dataset/
│   └── LOLv2_Real.yaml        # 数据集配置
├── trainer/
│   └── default_trainer.yaml   # 训练器配置
└── task/
    ├── train.yaml             # 训练任务配置
    ├── evaluate.yaml          # 评估任务配置
    └── inference.yaml         # 推理任务配置
```

---

## 2. 具体修改场景详细步骤

### 2.1 替换注意力模块

#### 场景：从SEBlock改为CBAM（Convolutional Block Attention Module）

**步骤1：创建新的注意力模块**

```python
# 在 src/llie/models/components/common.py 中添加
class CBAMBlock(nn.Module):
    """CBAM注意力模块：结合通道注意力和空间注意力"""
    
    def __init__(self, channels: int, reduction: int = 16, kernel_size: int = 7):
        super().__init__()
        # 通道注意力
        self.channel_attention = ChannelAttention(channels, reduction)
        # 空间注意力  
        self.spatial_attention = SpatialAttention(kernel_size)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 先应用通道注意力
        x = self.channel_attention(x)
        # 再应用空间注意力
        x = self.spatial_attention(x)
        return x

class ChannelAttention(nn.Module):
    """通道注意力模块"""
    def __init__(self, channels: int, reduction: int = 16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        attention = self.sigmoid(avg_out + max_out)
        return x * attention
```

**步骤2：更新组件导出**

```python
# 在 src/llie/models/components/__init__.py 中添加
from .common import CBAMBlock, ChannelAttention

__all__ = [
    # 现有组件...
    "CBAMBlock",
    "ChannelAttention",
    # 其他组件...
]
```

**步骤3：替换模型中的注意力模块**

```python
# 在需要使用的模型文件中（如 fourier_blocks.py）
# 将 SEBlock 替换为 CBAMBlock
self.attention = CBAMBlock(channels)  # 替换原来的 SEBlock(channels)
```

### 2.2 删除或添加网络分支

#### 场景：在DMFourLLIE中添加边缘检测分支

**步骤1：创建边缘检测组件**

```python
# 在 src/llie/models/components/common.py 中添加
class EdgeDetectionBranch(nn.Module):
    """边缘检测分支，用于保持图像边缘信息"""
    
    def __init__(self, input_channels: int = 3, edge_channels: int = 16):
        super().__init__()
        # Sobel算子用于边缘检测
        self.sobel_x = nn.Conv2d(1, 1, 3, padding=1, bias=False)
        self.sobel_y = nn.Conv2d(1, 1, 3, padding=1, bias=False)
        
        # 初始化Sobel核
        sobel_x_kernel = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y_kernel = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        
        self.sobel_x.weight.data = sobel_x_kernel.view(1, 1, 3, 3)
        self.sobel_y.weight.data = sobel_y_kernel.view(1, 1, 3, 3)
        
        # 边缘特征处理
        self.edge_processor = nn.Sequential(
            nn.Conv2d(2, edge_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(edge_channels, edge_channels, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 转换为灰度图
        gray = 0.299 * x[:, 0:1] + 0.587 * x[:, 1:2] + 0.114 * x[:, 2:3]
        
        # 计算边缘
        edge_x = self.sobel_x(gray)
        edge_y = self.sobel_y(gray)
        
        # 合并边缘信息
        edges = torch.cat([edge_x, edge_y], dim=1)
        
        # 处理边缘特征
        edge_features = self.edge_processor(edges)
        
        return edge_features
```

**步骤2：修改主模型添加边缘分支**

```python
# 在 src/llie/models/dmfourllie.py 中修改
class DMFourLLIE(BaseArchitecture):
    def __init__(self, **kwargs):
        super().__init__()
        # 现有初始化代码...
        
        # 添加边缘检测分支
        self.edge_branch = EdgeDetectionBranch(
            input_channels=self.input_channels,
            edge_channels=16
        )
        
        # 修改最终融合层以包含边缘特征
        self.final_fusion = nn.Conv2d(
            self.s_channels + 16,  # 原始通道数 + 边缘特征通道数
            self.input_channels, 
            1, 
            bias=True
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 现有处理流程...
        
        # 添加边缘检测
        edge_features = self.edge_branch(x)
        
        # 在最终融合时包含边缘特征
        # 假设 spatial_enhanced 是空间处理的输出
        combined_features = torch.cat([spatial_enhanced, edge_features], dim=1)
        final_output = self.final_fusion(combined_features)
        
        return final_output + x  # 残差连接
```

### 2.3 更换色彩空间

#### 场景：从RGB转换为HSV色彩空间处理

**步骤1：创建色彩空间转换工具**

```python
# 在 src/llie/models/components/common.py 中添加
class ColorSpaceConverter(nn.Module):
    """色彩空间转换模块"""
    
    @staticmethod
    def rgb_to_hsv(rgb: torch.Tensor) -> torch.Tensor:
        """RGB转HSV色彩空间"""
        r, g, b = rgb[:, 0], rgb[:, 1], rgb[:, 2]
        
        max_val, max_idx = torch.max(rgb, dim=1)
        min_val, _ = torch.min(rgb, dim=1)
        delta = max_val - min_val
        
        # 计算色调(H)
        h = torch.zeros_like(max_val)
        mask = delta != 0
        
        # 红色为最大值
        r_mask = (max_idx == 0) & mask
        h[r_mask] = ((g[r_mask] - b[r_mask]) / delta[r_mask]) % 6
        
        # 绿色为最大值
        g_mask = (max_idx == 1) & mask
        h[g_mask] = (b[g_mask] - r[g_mask]) / delta[g_mask] + 2
        
        # 蓝色为最大值
        b_mask = (max_idx == 2) & mask
        h[b_mask] = (r[b_mask] - g[b_mask]) / delta[b_mask] + 4
        
        h = h / 6.0  # 归一化到[0,1]
        
        # 计算饱和度(S)
        s = torch.zeros_like(max_val)
        s[max_val != 0] = delta[max_val != 0] / max_val[max_val != 0]
        
        # 计算明度(V)
        v = max_val
        
        return torch.stack([h, s, v], dim=1)
    
    @staticmethod
    def hsv_to_rgb(hsv: torch.Tensor) -> torch.Tensor:
        """HSV转RGB色彩空间"""
        h, s, v = hsv[:, 0], hsv[:, 1], hsv[:, 2]
        
        h = h * 6.0  # 恢复到[0,6]范围
        i = torch.floor(h).long()
        f = h - i
        
        p = v * (1 - s)
        q = v * (1 - s * f)
        t = v * (1 - s * (1 - f))
        
        # 根据色调区间计算RGB
        rgb = torch.zeros_like(hsv)
        
        # 区间0: R=V, G=T, B=P
        mask0 = (i % 6) == 0
        rgb[mask0, 0] = v[mask0]
        rgb[mask0, 1] = t[mask0]
        rgb[mask0, 2] = p[mask0]
        
        # 区间1: R=Q, G=V, B=P
        mask1 = (i % 6) == 1
        rgb[mask1, 0] = q[mask1]
        rgb[mask1, 1] = v[mask1]
        rgb[mask1, 2] = p[mask1]
        
        # 其他区间类似...
        
        return rgb
```

**步骤2：修改模型使用HSV处理**

```python
# 在 src/llie/models/dmfourllie.py 中修改
class DMFourLLIE(BaseArchitecture):
    def __init__(self, **kwargs):
        super().__init__()
        # 添加色彩空间转换器
        self.color_converter = ColorSpaceConverter()
        
        # 其他初始化...
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 转换到HSV色彩空间
        hsv_input = self.color_converter.rgb_to_hsv(x)
        
        # 分别处理HSV通道
        h_channel = hsv_input[:, 0:1]  # 色调
        s_channel = hsv_input[:, 1:2]  # 饱和度  
        v_channel = hsv_input[:, 2:3]  # 明度
        
        # 主要在明度通道进行增强
        enhanced_v = self.luminance_processor(v_channel)
        
        # 重新组合HSV
        enhanced_hsv = torch.cat([h_channel, s_channel, enhanced_v], dim=1)
        
        # 转换回RGB
        enhanced_rgb = self.color_converter.hsv_to_rgb(enhanced_hsv)
        
        return enhanced_rgb
```

### 2.4 修改损失函数

#### 场景：添加感知损失和边缘保持损失

**步骤1：创建新的损失函数**

```python
# 在 src/llie/losses/__init__.py 中添加
@register_loss("EnhancedCombinedLoss")
class EnhancedCombinedLoss(nn.Module):
    """增强的组合损失函数，包含感知损失和边缘保持损失"""
    
    def __init__(
        self,
        pixel_weight: float = 1.0,
        perceptual_weight: float = 0.1,
        edge_weight: float = 0.1,
        ssim_weight: float = 0.1,
        **kwargs
    ):
        super().__init__()
        self.pixel_weight = pixel_weight
        self.perceptual_weight = perceptual_weight
        self.edge_weight = edge_weight
        self.ssim_weight = ssim_weight
        
        # 像素损失
        self.pixel_loss = nn.L1Loss()
        
        # 感知损失（VGG特征）
        self.perceptual_loss = PerceptualLoss()
        
        # 边缘保持损失
        self.edge_loss = EdgePreservationLoss()
        
        # SSIM损失
        self.ssim_loss = SSIMLoss()
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, torch.Tensor]:
        losses = {}
        
        # 像素损失
        losses['pixel'] = self.pixel_loss(pred, target) * self.pixel_weight
        
        # 感知损失
        losses['perceptual'] = self.perceptual_loss(pred, target) * self.perceptual_weight
        
        # 边缘保持损失
        losses['edge'] = self.edge_loss(pred, target) * self.edge_weight
        
        # SSIM损失
        losses['ssim'] = (1 - self.ssim_loss(pred, target)) * self.ssim_weight
        
        # 总损失
        losses['total'] = sum(losses.values())
        
        return losses

class EdgePreservationLoss(nn.Module):
    """边缘保持损失"""
    
    def __init__(self):
        super().__init__()
        # Sobel算子
        self.sobel_x = nn.Conv2d(1, 1, 3, padding=1, bias=False)
        self.sobel_y = nn.Conv2d(1, 1, 3, padding=1, bias=False)
        
        # 初始化Sobel核
        sobel_x_kernel = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y_kernel = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        
        self.sobel_x.weight.data = sobel_x_kernel.view(1, 1, 3, 3)
        self.sobel_y.weight.data = sobel_y_kernel.view(1, 1, 3, 3)
        
        # 冻结参数
        for param in self.parameters():
            param.requires_grad = False
            
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 转换为灰度图
        pred_gray = 0.299 * pred[:, 0:1] + 0.587 * pred[:, 1:2] + 0.114 * pred[:, 2:3]
        target_gray = 0.299 * target[:, 0:1] + 0.587 * target[:, 1:2] + 0.114 * target[:, 2:3]
        
        # 计算边缘
        pred_edge_x = self.sobel_x(pred_gray)
        pred_edge_y = self.sobel_y(pred_gray)
        target_edge_x = self.sobel_x(target_gray)
        target_edge_y = self.sobel_y(target_gray)
        
        # 边缘损失
        edge_loss = F.l1_loss(pred_edge_x, target_edge_x) + F.l1_loss(pred_edge_y, target_edge_y)
        
        return edge_loss
```

**步骤2：更新配置文件**

```yaml
# 在 configs/trainer/default_trainer.yaml 中修改
loss:
  type: EnhancedCombinedLoss
  pixel_weight: 1.0
  perceptual_weight: 0.1
  edge_weight: 0.1
  ssim_weight: 0.1
```

### 2.5 调整网络层数和通道数

#### 场景：增加网络容量以处理更复杂的场景

**步骤1：修改配置文件**

```yaml
# 在 configs/model/dmfourllie.yaml 中修改
architecture:
  # 增加通道数
  y_nf: 32          # 从16增加到32
  f_nf: 32          # 从16增加到32  
  s_nf: 64          # 从32增加到64
  
  # 增加处理块数量
  num_blocks: 8     # 从6增加到8
  
components:
  luminance_map:
    depth: [2, 2, 2, 2]  # 从[1,1,1,1]增加到[2,2,2,2]
```

**步骤2：验证模型参数量变化**

```python
# 创建测试脚本验证参数量
def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

# 测试不同配置的参数量
original_config = {"y_nf": 16, "f_nf": 16, "s_nf": 32, "num_blocks": 6}
enhanced_config = {"y_nf": 32, "f_nf": 32, "s_nf": 64, "num_blocks": 8}

print(f"原始模型参数量: {count_parameters(original_model):,}")
print(f"增强模型参数量: {count_parameters(enhanced_model):,}")
```

---

## 3. 文件修改指南

### 3.1 需要修改的核心文件清单

| 文件路径 | 修改目的 | 关键修改点 |
|---------|---------|-----------|
| `src/llie/models/components/common.py` | 添加新组件 | 新的注意力机制、色彩空间转换等 |
| `src/llie/models/components/__init__.py` | 导出新组件 | 更新`__all__`列表 |
| `src/llie/models/dmfourllie.py` | 修改主模型 | 网络结构、前向传播逻辑 |
| `src/llie/losses/__init__.py` | 修改损失函数 | 新的损失函数定义和注册 |
| `configs/model/dmfourllie.yaml` | 调整模型参数 | 通道数、层数、组件配置 |
| `configs/trainer/default_trainer.yaml` | 调整训练参数 | 损失函数、优化器设置 |

### 3.2 关键代码段详解

#### 3.2.1 模型注册机制

```python
# 所有新模型都需要使用注册装饰器
from ..utils.registry import register_model

@register_model("YourNewModel")
class YourNewModel(BaseArchitecture):
    def __init__(self, **kwargs):
        super().__init__()
        # 模型实现
```

#### 3.2.2 组件导入和使用

```python
# 在模型文件中导入组件
from .components.common import SEBlock, CBAMBlock
from .components.fourier_blocks import FFTProcessBlock

# 在模型中使用
self.attention = CBAMBlock(channels=64)
self.fft_block = FFTProcessBlock(channels=32)
```

#### 3.2.3 配置文件参数引用

```yaml
# 使用变量引用避免重复
architecture:
  base_channels: 32

components:
  luminance_map:
    channels: ${model.architecture.base_channels}  # 引用上面定义的值
```

### 3.3 配置文件参数对应关系

| 配置参数 | 对应代码位置 | 作用说明 |
|---------|-------------|---------|
| `architecture.y_nf` | `DMFourLLIE.__init__()` | 亮度处理通道数 |
| `architecture.f_nf` | `FirstProcessModel` | 傅里叶处理通道数 |
| `architecture.s_nf` | `SecondProcessModel` | 空间处理通道数 |
| `architecture.num_blocks` | 各处理模块 | 处理块数量 |
| `components.luminance_map.depth` | `LuminanceMapProcessor` | 各层深度配置 |

---

## 4. 验证和测试流程

### 4.1 模型结构正确性验证

**步骤1：创建模型验证脚本**

```python
# scripts/validate_model_structure.py
import torch
from omegaconf import OmegaConf
from src.llie.utils.registry import MODELS

def validate_model_structure(config_path: str):
    """验证模型结构的正确性"""
    
    # 加载配置
    config = OmegaConf.load(config_path)
    
    # 构建模型
    model = MODELS.build(config.model)
    
    # 测试输入
    test_input = torch.randn(1, 3, 256, 256)
    
    try:
        # 前向传播测试
        with torch.no_grad():
            output = model(test_input)
            
        print(f"✅ 模型结构验证成功")
        print(f"   输入形状: {test_input.shape}")
        print(f"   输出形状: {output.shape}")
        print(f"   参数量: {sum(p.numel() for p in model.parameters()):,}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型结构验证失败: {e}")
        return False

if __name__ == "__main__":
    validate_model_structure("configs/model/dmfourllie.yaml")
```

**步骤2：运行验证脚本**

```bash
python scripts/validate_model_structure.py
```

### 4.2 快速训练测试

**步骤1：创建快速测试配置**

```yaml
# configs/test/quick_test.yaml
defaults:
  - base_config
  - override trainer: quick_test_trainer

trainer:
  max_epochs: 2
  batch_size: 2
  
dataset:
  train:
    data_root: "data/LOLv2/Train"
    max_samples: 10  # 限制样本数量
```

**步骤2：运行快速测试**

```bash
# 快速训练测试
python main.py --config-name=quick_test

# 检查是否能正常保存和加载模型
python main.py task=evaluate evaluation.checkpoint_path=outputs/models/latest_checkpoint.pth
```

### 4.3 常见错误和调试方法

#### 4.3.1 形状不匹配错误

```python
# 调试技巧：在模型中添加形状打印
def forward(self, x):
    print(f"Input shape: {x.shape}")
    
    x = self.conv1(x)
    print(f"After conv1: {x.shape}")
    
    # 继续处理...
```

#### 4.3.2 内存溢出错误

```python
# 检查模型参数量和中间特征图大小
def analyze_memory_usage(model, input_size=(1, 3, 256, 256)):
    """分析模型内存使用"""
    
    # 参数内存
    param_memory = sum(p.numel() * 4 for p in model.parameters()) / 1024**2  # MB
    
    # 特征图内存（估算）
    feature_memory = torch.prod(torch.tensor(input_size)) * 4 / 1024**2  # MB
    
    print(f"参数内存: {param_memory:.2f} MB")
    print(f"特征图内存（估算）: {feature_memory:.2f} MB")
```

#### 4.3.3 梯度消失/爆炸

```python
# 检查梯度范数
def check_gradients(model):
    """检查模型梯度"""
    total_norm = 0
    for p in model.parameters():
        if p.grad is not None:
            param_norm = p.grad.data.norm(2)
            total_norm += param_norm.item() ** 2
    total_norm = total_norm ** (1. / 2)
    
    print(f"梯度范数: {total_norm:.6f}")
    
    if total_norm > 10:
        print("⚠️  可能存在梯度爆炸")
    elif total_norm < 1e-6:
        print("⚠️  可能存在梯度消失")
```

---

## 5. 最佳实践建议

### 5.1 版本控制策略

#### 5.1.1 分支管理

```bash
# 为每个重大修改创建特性分支
git checkout -b feature/add-cbam-attention
git checkout -b feature/hsv-color-space
git checkout -b feature/enhanced-loss-function

# 完成修改后合并到主分支
git checkout main
git merge feature/add-cbam-attention
```

#### 5.1.2 提交信息规范

```bash
# 使用标准化的提交信息格式
git commit -m "feat(model): 添加CBAM注意力机制

- 在common.py中实现CBAMBlock
- 替换DMFourLLIE中的SEBlock
- 更新配置文件支持CBAM参数
- 添加相应的单元测试

性能提升: PSNR +0.5dB, SSIM +0.02"
```

### 5.2 实验记录方法

#### 5.2.1 实验配置管理

```python
# 创建实验配置模板
# configs/experiments/cbam_attention_exp.yaml
# @package _global_
defaults:
  - base_config
  - override model: dmfourllie_cbam

experiment:
  name: "CBAM_Attention_Experiment"
  description: "测试CBAM注意力机制对LLIE性能的影响"
  tags: ["attention", "cbam", "ablation"]
  
model:
  architecture:
    attention_type: "cbam"  # 新增参数
    
wandb:
  tags: ["cbam", "attention", "experiment"]
  notes: "CBAM注意力机制消融实验"
```

#### 5.2.2 结果记录模板

```markdown
# 实验记录模板
## 实验信息
- **实验名称**: CBAM注意力机制测试
- **日期**: 2025-01-31
- **分支**: feature/add-cbam-attention
- **提交哈希**: abc123def

## 修改内容
1. 添加CBAMBlock实现
2. 替换原有SEBlock
3. 调整模型配置参数

## 实验结果
| 指标 | 基线模型 | CBAM模型 | 改进 |
|------|---------|---------|------|
| PSNR | 24.5 dB | 25.0 dB | +0.5 dB |
| SSIM | 0.85 | 0.87 | +0.02 |
| 参数量 | 2.1M | 2.3M | +0.2M |

## 结论和下一步
- CBAM注意力机制带来了明显的性能提升
- 参数量增加可接受
- 下一步: 测试不同的reduction ratio参数
```

### 5.3 代码注释规范

#### 5.3.1 模块级注释

```python
"""
CBAM注意力机制实现模块

本模块实现了Convolutional Block Attention Module (CBAM)，
结合了通道注意力和空间注意力机制，用于提升特征表示能力。

主要组件:
- CBAMBlock: 完整的CBAM注意力模块
- ChannelAttention: 通道注意力子模块  
- SpatialAttention: 空间注意力子模块

参考文献:
- CBAM: Convolutional Block Attention Module (ECCV 2018)

作者: OGAS
创建时间: 2025-01-31
"""
```

#### 5.3.2 函数级注释

```python
def forward(self, x: torch.Tensor) -> torch.Tensor:
    """
    CBAM前向传播
    
    处理流程:
    1. 应用通道注意力增强重要通道
    2. 应用空间注意力突出重要区域
    3. 返回注意力加权后的特征
    
    参数:
        x: 输入特征图 [B, C, H, W]
        
    返回:
        注意力加权后的特征图 [B, C, H, W]
        
    示例:
        >>> cbam = CBAMBlock(64)
        >>> x = torch.randn(4, 64, 32, 32)
        >>> out = cbam(x)  # 形状保持不变
    """
```

### 5.4 性能优化建议

#### 5.4.1 内存优化

```python
# 使用梯度检查点减少内存使用
from torch.utils.checkpoint import checkpoint

def forward(self, x):
    # 对计算密集的块使用检查点
    x = checkpoint(self.heavy_computation_block, x)
    return x

# 及时释放不需要的中间变量
def forward(self, x):
    intermediate = self.conv1(x)
    result = self.conv2(intermediate)
    del intermediate  # 显式释放内存
    return result
```

#### 5.4.2 计算优化

```python
# 使用inplace操作减少内存分配
self.relu = nn.ReLU(inplace=True)

# 合并连续的卷积操作
self.conv_block = nn.Sequential(
    nn.Conv2d(in_ch, out_ch, 3, padding=1),
    nn.BatchNorm2d(out_ch),
    nn.ReLU(inplace=True)
)
```

---

## 📝 总结

本教程提供了LLIE项目模型架构修改的完整指南，涵盖了从简单的组件替换到复杂的网络结构重设计。

**关键要点:**
1. **模块化设计**: 充分利用项目的模块化架构，通过组合现有组件快速实现新功能
2. **配置驱动**: 优先通过配置文件调整参数，减少代码修改
3. **渐进式修改**: 从小的修改开始，逐步验证，避免一次性大幅修改
4. **完整测试**: 每次修改后都要进行结构验证和快速训练测试
5. **详细记录**: 记录所有修改和实验结果，便于后续分析和复现

通过遵循本教程的指导，您可以安全、高效地进行各种网络结构修改实验，推动LLIE技术的发展。

---

## 附录A: 常用模型组件库

### A.1 注意力机制组件

```python
# src/llie/models/components/attention.py
"""常用注意力机制组件库"""

class MultiHeadSelfAttention(nn.Module):
    """多头自注意力机制"""

    def __init__(self, channels: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        assert channels % num_heads == 0

        self.channels = channels
        self.num_heads = num_heads
        self.head_dim = channels // num_heads

        self.qkv = nn.Linear(channels, channels * 3)
        self.proj = nn.Linear(channels, channels)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        B, C, H, W = x.shape
        x_flat = x.view(B, C, H * W).transpose(1, 2)  # [B, HW, C]

        qkv = self.qkv(x_flat).reshape(B, H * W, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # [3, B, num_heads, HW, head_dim]
        q, k, v = qkv[0], qkv[1], qkv[2]

        # 计算注意力
        attn = (q @ k.transpose(-2, -1)) * (self.head_dim ** -0.5)
        attn = F.softmax(attn, dim=-1)
        attn = self.dropout(attn)

        # 应用注意力
        out = (attn @ v).transpose(1, 2).reshape(B, H * W, C)
        out = self.proj(out)

        return out.transpose(1, 2).view(B, C, H, W)

class CrossAttention(nn.Module):
    """跨模态注意力机制"""

    def __init__(self, query_dim: int, key_dim: int, value_dim: int, output_dim: int):
        super().__init__()
        self.query_proj = nn.Linear(query_dim, output_dim)
        self.key_proj = nn.Linear(key_dim, output_dim)
        self.value_proj = nn.Linear(value_dim, output_dim)
        self.scale = output_dim ** -0.5

    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor) -> torch.Tensor:
        B, C_q, H, W = query.shape

        # 展平空间维度
        q = self.query_proj(query.view(B, C_q, -1).transpose(1, 2))  # [B, HW, output_dim]
        k = self.key_proj(key.view(B, -1, H * W).transpose(1, 2))    # [B, HW, output_dim]
        v = self.value_proj(value.view(B, -1, H * W).transpose(1, 2)) # [B, HW, output_dim]

        # 计算注意力权重
        attn = torch.bmm(q, k.transpose(1, 2)) * self.scale  # [B, HW, HW]
        attn = F.softmax(attn, dim=-1)

        # 应用注意力
        out = torch.bmm(attn, v)  # [B, HW, output_dim]

        return out.transpose(1, 2).view(B, -1, H, W)
```

### A.2 特征融合组件

```python
class AdaptiveFeatureFusion(nn.Module):
    """自适应特征融合模块"""

    def __init__(self, channels: int, num_features: int = 2):
        super().__init__()
        self.num_features = num_features

        # 特征权重生成网络
        self.weight_net = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels * num_features, channels // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 4, num_features, 1),
            nn.Softmax(dim=1)
        )

    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        assert len(features) == self.num_features

        # 连接所有特征
        concat_features = torch.cat(features, dim=1)

        # 计算权重
        weights = self.weight_net(concat_features)  # [B, num_features, 1, 1]

        # 加权融合
        fused = sum(w.unsqueeze(1) * f for w, f in zip(weights.unbind(1), features))

        return fused

class ProgressiveFeatureFusion(nn.Module):
    """渐进式特征融合"""

    def __init__(self, channels: int, num_stages: int = 4):
        super().__init__()
        self.num_stages = num_stages

        # 每个阶段的融合模块
        self.fusion_modules = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(channels * 2, channels, 3, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(channels, channels, 3, padding=1)
            ) for _ in range(num_stages - 1)
        ])

    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        assert len(features) == self.num_stages

        # 从最后一个特征开始，逐步融合
        result = features[-1]

        for i in range(self.num_stages - 2, -1, -1):
            # 上采样到当前尺度
            if result.shape[-2:] != features[i].shape[-2:]:
                result = F.interpolate(result, size=features[i].shape[-2:], mode='bilinear', align_corners=False)

            # 融合特征
            combined = torch.cat([features[i], result], dim=1)
            result = self.fusion_modules[i](combined) + features[i]  # 残差连接

        return result
```

### A.3 频域处理组件

```python
class WaveletTransform(nn.Module):
    """小波变换处理模块"""

    def __init__(self, channels: int, wavelet_type: str = 'haar'):
        super().__init__()
        self.channels = channels
        self.wavelet_type = wavelet_type

        # 小波系数处理网络
        self.ll_processor = nn.Conv2d(channels, channels, 3, padding=1)
        self.lh_processor = nn.Conv2d(channels, channels, 3, padding=1)
        self.hl_processor = nn.Conv2d(channels, channels, 3, padding=1)
        self.hh_processor = nn.Conv2d(channels, channels, 3, padding=1)

        # 重构网络
        self.reconstruction = nn.Conv2d(channels * 4, channels, 1)

    def dwt2d(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """二维离散小波变换"""
        # 简化的Haar小波变换实现
        x_ll = F.avg_pool2d(x, 2)
        x_lh = F.avg_pool2d(x[:, :, :, 1:] - x[:, :, :, :-1], 2)
        x_hl = F.avg_pool2d(x[:, :, 1:, :] - x[:, :, :-1, :], 2)
        x_hh = F.avg_pool2d((x[:, :, 1:, 1:] - x[:, :, 1:, :-1]) - (x[:, :, :-1, 1:] - x[:, :, :-1, :-1]), 2)

        return x_ll, x_lh, x_hl, x_hh

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 小波分解
        ll, lh, hl, hh = self.dwt2d(x)

        # 分别处理各个子带
        ll_processed = self.ll_processor(ll)
        lh_processed = self.lh_processor(lh)
        hl_processed = self.hl_processor(hl)
        hh_processed = self.hh_processor(hh)

        # 上采样到原始尺寸
        ll_up = F.interpolate(ll_processed, size=x.shape[-2:], mode='bilinear', align_corners=False)
        lh_up = F.interpolate(lh_processed, size=x.shape[-2:], mode='bilinear', align_corners=False)
        hl_up = F.interpolate(hl_processed, size=x.shape[-2:], mode='bilinear', align_corners=False)
        hh_up = F.interpolate(hh_processed, size=x.shape[-2:], mode='bilinear', align_corners=False)

        # 重构
        reconstructed = self.reconstruction(torch.cat([ll_up, lh_up, hl_up, hh_up], dim=1))

        return reconstructed + x  # 残差连接

class DCTProcessor(nn.Module):
    """DCT频域处理模块"""

    def __init__(self, channels: int, block_size: int = 8):
        super().__init__()
        self.channels = channels
        self.block_size = block_size

        # DCT系数处理网络
        self.dct_processor = nn.Sequential(
            nn.Conv2d(channels, channels * 2, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels * 2, channels, 3, padding=1)
        )

    def block_dct2d(self, x: torch.Tensor) -> torch.Tensor:
        """分块DCT变换"""
        B, C, H, W = x.shape
        block_size = self.block_size

        # 确保尺寸是block_size的倍数
        pad_h = (block_size - H % block_size) % block_size
        pad_w = (block_size - W % block_size) % block_size

        if pad_h > 0 or pad_w > 0:
            x = F.pad(x, (0, pad_w, 0, pad_h), mode='reflect')

        H_pad, W_pad = x.shape[-2:]

        # 分块处理
        x_blocks = x.unfold(2, block_size, block_size).unfold(3, block_size, block_size)
        x_blocks = x_blocks.contiguous().view(B, C, -1, block_size, block_size)

        # 对每个块应用DCT（这里使用简化实现）
        dct_blocks = torch.fft.dct(torch.fft.dct(x_blocks, dim=-1), dim=-2)

        # 重新组合
        num_blocks_h = H_pad // block_size
        num_blocks_w = W_pad // block_size

        dct_blocks = dct_blocks.view(B, C, num_blocks_h, num_blocks_w, block_size, block_size)
        dct_result = dct_blocks.permute(0, 1, 2, 4, 3, 5).contiguous()
        dct_result = dct_result.view(B, C, H_pad, W_pad)

        # 移除padding
        if pad_h > 0 or pad_w > 0:
            dct_result = dct_result[:, :, :H, :W]

        return dct_result

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # DCT变换
        dct_coeffs = self.block_dct2d(x)

        # 处理DCT系数
        processed_coeffs = self.dct_processor(dct_coeffs)

        # 逆DCT变换
        reconstructed = self.block_dct2d(processed_coeffs)  # 简化：使用相同函数

        return reconstructed + x  # 残差连接
```

---

## 附录B: 调试和性能分析工具

### B.1 模型分析工具

```python
# scripts/model_analysis.py
"""模型分析和调试工具"""

import torch
import torch.nn as nn
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

class ModelAnalyzer:
    """模型分析器"""

    def __init__(self, model: nn.Module):
        self.model = model
        self.hooks = []
        self.activations = {}
        self.gradients = {}

    def register_hooks(self):
        """注册钩子函数收集中间结果"""

        def forward_hook(name):
            def hook(module, input, output):
                self.activations[name] = output.detach()
            return hook

        def backward_hook(name):
            def hook(module, grad_input, grad_output):
                if grad_output[0] is not None:
                    self.gradients[name] = grad_output[0].detach()
            return hook

        for name, module in self.model.named_modules():
            if isinstance(module, (nn.Conv2d, nn.Linear)):
                handle1 = module.register_forward_hook(forward_hook(name))
                handle2 = module.register_backward_hook(backward_hook(name))
                self.hooks.extend([handle1, handle2])

    def remove_hooks(self):
        """移除所有钩子"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()

    def analyze_activations(self, x: torch.Tensor) -> Dict:
        """分析激活值分布"""
        self.register_hooks()

        # 前向传播
        output = self.model(x)

        # 分析激活值
        analysis = {}
        for name, activation in self.activations.items():
            analysis[name] = {
                'mean': activation.mean().item(),
                'std': activation.std().item(),
                'min': activation.min().item(),
                'max': activation.max().item(),
                'zero_ratio': (activation == 0).float().mean().item()
            }

        self.remove_hooks()
        return analysis

    def analyze_gradients(self, x: torch.Tensor, target: torch.Tensor, loss_fn) -> Dict:
        """分析梯度分布"""
        self.register_hooks()

        # 前向传播
        output = self.model(x)
        loss = loss_fn(output, target)

        # 反向传播
        loss.backward()

        # 分析梯度
        analysis = {}
        for name, gradient in self.gradients.items():
            analysis[name] = {
                'mean': gradient.mean().item(),
                'std': gradient.std().item(),
                'norm': gradient.norm().item(),
                'zero_ratio': (gradient == 0).float().mean().item()
            }

        self.remove_hooks()
        return analysis

    def plot_activation_distribution(self, save_path: str = None):
        """绘制激活值分布图"""
        if not self.activations:
            print("请先运行analyze_activations")
            return

        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        axes = axes.flatten()

        layer_names = list(self.activations.keys())[:4]  # 只显示前4层

        for i, name in enumerate(layer_names):
            activation = self.activations[name].cpu().numpy().flatten()
            axes[i].hist(activation, bins=50, alpha=0.7)
            axes[i].set_title(f'{name}')
            axes[i].set_xlabel('Activation Value')
            axes[i].set_ylabel('Frequency')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path)
        plt.show()

def profile_model_speed(model: nn.Module, input_size: Tuple[int, ...], num_runs: int = 100):
    """性能分析：测量模型推理速度"""
    import time

    model.eval()
    device = next(model.parameters()).device

    # 预热
    dummy_input = torch.randn(input_size).to(device)
    for _ in range(10):
        with torch.no_grad():
            _ = model(dummy_input)

    # 测量时间
    torch.cuda.synchronize() if device.type == 'cuda' else None
    start_time = time.time()

    for _ in range(num_runs):
        with torch.no_grad():
            _ = model(dummy_input)

    torch.cuda.synchronize() if device.type == 'cuda' else None
    end_time = time.time()

    avg_time = (end_time - start_time) / num_runs
    fps = 1.0 / avg_time

    print(f"平均推理时间: {avg_time*1000:.2f} ms")
    print(f"推理速度: {fps:.2f} FPS")

    return avg_time, fps

def analyze_model_complexity(model: nn.Module, input_size: Tuple[int, ...]):
    """分析模型复杂度"""
    from thop import profile, clever_format

    dummy_input = torch.randn(input_size)
    flops, params = profile(model, inputs=(dummy_input,), verbose=False)

    flops, params = clever_format([flops, params], "%.3f")

    print(f"模型参数量: {params}")
    print(f"计算复杂度: {flops}")

    return flops, params
```

### B.2 训练监控工具

```python
# src/llie/utils/training_monitor.py
"""训练过程监控工具"""

import torch
import numpy as np
from typing import Dict, List
import matplotlib.pyplot as plt
from collections import defaultdict, deque

class TrainingMonitor:
    """训练过程监控器"""

    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.metrics_history = defaultdict(list)
        self.recent_metrics = defaultdict(lambda: deque(maxlen=window_size))

    def update(self, metrics: Dict[str, float], step: int):
        """更新监控指标"""
        for key, value in metrics.items():
            self.metrics_history[key].append((step, value))
            self.recent_metrics[key].append(value)

    def get_recent_average(self, metric_name: str) -> float:
        """获取最近的平均值"""
        if metric_name not in self.recent_metrics:
            return 0.0
        return np.mean(list(self.recent_metrics[metric_name]))

    def detect_anomalies(self, metric_name: str, threshold: float = 3.0) -> bool:
        """检测异常值（基于标准差）"""
        if len(self.recent_metrics[metric_name]) < 10:
            return False

        recent_values = list(self.recent_metrics[metric_name])
        mean_val = np.mean(recent_values)
        std_val = np.std(recent_values)

        latest_value = recent_values[-1]
        z_score = abs(latest_value - mean_val) / (std_val + 1e-8)

        return z_score > threshold

    def plot_metrics(self, save_path: str = None):
        """绘制训练曲线"""
        num_metrics = len(self.metrics_history)
        if num_metrics == 0:
            return

        fig, axes = plt.subplots((num_metrics + 1) // 2, 2, figsize=(12, 4 * ((num_metrics + 1) // 2)))
        if num_metrics == 1:
            axes = [axes]
        elif num_metrics <= 2:
            axes = axes.flatten()
        else:
            axes = axes.flatten()

        for i, (metric_name, history) in enumerate(self.metrics_history.items()):
            if i >= len(axes):
                break

            steps, values = zip(*history)
            axes[i].plot(steps, values)
            axes[i].set_title(f'{metric_name}')
            axes[i].set_xlabel('Step')
            axes[i].set_ylabel('Value')
            axes[i].grid(True)

        # 隐藏多余的子图
        for i in range(num_metrics, len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path)
        plt.show()

class GradientMonitor:
    """梯度监控器"""

    def __init__(self, model: nn.Module):
        self.model = model
        self.gradient_norms = defaultdict(list)

    def update(self, step: int):
        """更新梯度信息"""
        total_norm = 0.0

        for name, param in self.model.named_parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2).item()
                self.gradient_norms[name].append((step, param_norm))
                total_norm += param_norm ** 2

        total_norm = total_norm ** 0.5
        self.gradient_norms['total'].append((step, total_norm))

    def check_gradient_health(self) -> Dict[str, str]:
        """检查梯度健康状况"""
        health_report = {}

        for name, history in self.gradient_norms.items():
            if len(history) < 5:
                continue

            recent_norms = [norm for _, norm in history[-10:]]
            avg_norm = np.mean(recent_norms)

            if avg_norm > 10.0:
                health_report[name] = "梯度可能爆炸"
            elif avg_norm < 1e-6:
                health_report[name] = "梯度可能消失"
            else:
                health_report[name] = "正常"

        return health_report
```

---

## 附录C: 实验配置模板

### C.1 消融实验配置

```yaml
# configs/experiments/ablation_study.yaml
# 消融实验配置模板

# @package _global_
defaults:
  - base_config
  - override model: dmfourllie

# 实验元信息
experiment:
  name: "DMFourLLIE_Ablation_Study"
  description: "DMFourLLIE各组件的消融实验"
  version: "v1.0"

# 实验变量（通过Hydra的多运行功能进行扫描）
hydra:
  mode: MULTIRUN
  sweeper:
    _target_: hydra._internal.core.plugins.create_config_search_path
    params:
      # 注意力机制消融
      model.components.attention_type: [none, se, cbam, self_attention]

      # 通道数消融
      model.architecture.s_nf: [16, 32, 64, 128]

      # 块数量消融
      model.architecture.num_blocks: [4, 6, 8, 10]

      # 损失函数消融
      trainer.loss.type: [L1Loss, CombinedLoss, EnhancedCombinedLoss]

# 训练配置（快速实验）
trainer:
  max_epochs: 50
  batch_size: 8

# 数据配置（小数据集）
dataset:
  train:
    max_samples: 1000
  val:
    max_samples: 200
```

### C.2 超参数搜索配置

```yaml
# configs/experiments/hyperparameter_search.yaml
# 超参数搜索配置

# @package _global_
defaults:
  - base_config
  - override model: dmfourllie

experiment:
  name: "Hyperparameter_Search"
  description: "DMFourLLIE超参数优化实验"

# 使用Optuna进行超参数搜索
hydra:
  mode: MULTIRUN
  sweeper:
    _target_: hydra_plugins.hydra_optuna_sweeper.optuna_sweeper.OptunaSweeper
    direction: maximize
    study_name: dmfourllie_optimization
    n_trials: 100

    params:
      # 学习率搜索
      trainer.optimizer.lr: tag(log, interval(1e-5, 1e-2))

      # 权重衰减搜索
      trainer.optimizer.weight_decay: tag(log, interval(1e-6, 1e-3))

      # 损失函数权重搜索
      trainer.loss.pixel_weight: interval(0.5, 2.0)
      trainer.loss.perceptual_weight: tag(log, interval(0.01, 0.5))
      trainer.loss.ssim_weight: tag(log, interval(0.01, 0.5))

      # 模型架构搜索
      model.architecture.s_nf: choice(32, 64, 96, 128)
      model.architecture.num_blocks: choice(4, 6, 8, 10)

# 早停配置（避免浪费计算资源）
trainer:
  early_stopping:
    patience: 10
    min_delta: 0.001
```

### C.3 对比实验配置

```yaml
# configs/experiments/comparison_study.yaml
# 与其他方法的对比实验

# @package _global_
defaults:
  - base_config

experiment:
  name: "Method_Comparison"
  description: "DMFourLLIE与其他LLIE方法的对比实验"

# 多个模型配置
models_to_compare:
  - name: "DMFourLLIE"
    config: "dmfourllie"

  - name: "DMFourLLIE_Lite"
    config: "dmfourllie_lite"  # 轻量版本

  - name: "Baseline_UNet"
    config: "baseline_unet"    # 基线方法

# 评估配置
evaluation:
  metrics: [psnr, ssim, mae, lpips]
  save_images: true
  generate_report: true

# 数据集配置（完整评估）
dataset:
  test_sets:
    - name: "LOLv2_Real"
      path: "data/LOLv2/Test"
    - name: "LOLv2_Synthetic"
      path: "data/LOLv2_Synthetic/Test"
    - name: "LIME"
      path: "data/LIME"
```

通过这些附录内容，您可以：

1. **快速集成常用组件** - 使用附录A中的组件库快速实现新功能
2. **深入分析模型行为** - 使用附录B中的工具进行详细的模型分析和调试
3. **系统化进行实验** - 使用附录C中的配置模板进行规范的实验设计

这些工具和模板将大大提高您的实验效率和代码质量。
