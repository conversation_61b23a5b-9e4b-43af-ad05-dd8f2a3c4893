1. 高度模块化: 每一个模块都封装到单独的模块, 方便即插即用 (plugin-to-play), 因为我要做大量的实验去找到性能指标最佳的模型. 其中实验包括增加/删除/修改/重复各种模块等操作, 所以我需要代码的高度模块化 (也许需要模块注册机制?使用注册器 pattern?)
2. 良好的可读性: 因为我要人工审查代码, 而且计划将这份代码作为一个学习范本, 给python基础和深度学习相关库(pytorch)基础都不扎实的后辈学习. (其实我也是基础不扎实的)
3. 工具链/第三方库现代化人性化: 希望使用强大的第三方库加速我的研究进程, 比如使用loguru代替logging得到更美观的输出, 使用各种现代的辅助训练的工具帮我找到下一步修改的方向等等
| 目标             | 工具                | 说明                                   |
| ---------------- | ------------------- | -------------------------------------- |
| 日志系统         | `loguru`            | 替代 `logging`，格式化美观，自动加时间 |
| 实验跟踪         | `wandb` 或 `MLflow` | 可视化 loss/PSNR/超参                  |
| 训练调度         | `tqdm` + `rich`     | 高质量进度条与控制台输出               |
| 配置管理         | `Hydra`             | 层级配置、动态组合、多实验 sweep       |
| 自动调参（可选） | `Optuna`            | 自动搜索最佳模型参数                   |

4. 参数配置文件化: 我不希望在命令行中打一大串的命令来作为本次训练/推理的参数, 而是希望采用配置文件记录超参数, 目录路径等等, 训练/推理脚本可以直接从中读取, 也方便了实验过程的记录
5. 实验记录系统化
每一次实验都应具备以下信息：
    - 使用的模块结构（配置文件 + Git 分支/commit）
    - 训练超参数与训练时间
    - 各阶段性能指标（PSNR、SSIM、LPIPS）
    - 可选：结果图像可视化输出
📦 建议工具：Weights & Biases（W&B）、MLflow、Excel 表格或 Markdown 卡片记录。
6. 脚本统一入口: 训练/推理/评估等都统一为模块化脚本，支持命令行调用、配置文件读取

### **1\. 项目愿景与核心目标**

本项目旨在将一个现有的低光增强领域的深度学习项目，重构为一个现代化、高效率、易于扩展的实验与研究框架。此框架不仅服务于当前的模型改进探索，也致力于成为一份高质量、可读性强的教学范本，帮助初学者快速掌握深度学习项目工程化的最佳实践。

### **2\. 设计哲学**

重构将遵循以下三大核心设计哲学：

* **模块化与解耦 (Modularity & Decoupling):** 框架中的所有核心组件（模型、损失函数、数据集等）都应是独立的、可替换的单元。通过“配置驱动”而非“代码修改”来组合不同的实验，实现真正的“即插即用”。  
* **可复现性与追踪 (Reproducibility & Tracking):** 每一次实验都必须是完全可复现的。必须能够精确追踪到任何一次结果所对应的代码版本、数据、超参数和环境，杜绝“炼丹”的玄学。  
* **开发者体验 (Developer Experience):** 借助现代化的工具链，最大化简化繁琐的重复性工作（如日志记录、参数调整、结果可视化），让研究者能将精力完全聚焦于算法和模型的创新。

### **3\. 需求规格清单**

| 编号 | 类别 | 需求描述 | 验收标准 (Acceptance Criteria) |
| :---- | :---- | :---- | :---- |
| **R1** | **核心架构** | **实现基于注册器的“即插即用”机制**采用注册器（Registry）模式统一管理模型、数据集、损失函数等所有可复用模块。 | 1\. 无需修改训练引擎等核心代码，仅通过修改配置文件即可替换模型中的主干网络或注意力模块。2. 新增一个自定义损失函数时，只需编写其实现并用装饰器注册，即可在配置文件中通过名称调用。 |
| **R2** | **代码质量** | **建立高标准的代码规范与文档**代码风格遵循 PEP 8，强制使用类型提示（Type Hinting），并为所有关键函数和类编写 Google 风格的 Docstrings。 | 1\. 项目代码能顺利通过 flake8 或 black 等 Linter 工具的检查。2. 主要模块、类和函数的文档字符串清晰说明了其功能、参数和返回值。3. 代码中无意义的“魔法数字”或硬编码字符串被常量或配置取代。 |
| **R3** | **工具链** | **集成现代化、自动化的辅助工具**全面采用 Loguru、Hydra、W\&B、Rich 等工具，构建自动化、可视化的开发与实验流程。 | 1\. 运行训练脚本时，控制台能看到 Rich 格式的多彩进度条和 Loguru 格式化的日志。2. 实验启动后，W\&B 平台能自动创建看板，并实时更新 Loss、PSNR 等指标曲线。3. Hydra 能自动管理输出目录，并为每次运行生成配置快照。 |
| **R4** | **配置管理** | **实现基于 Hydra 的层级化配置系统**所有参数（模型、训练、数据、路径等）均通过 .yaml 配置文件管理，支持配置组合、继承和命令行覆盖。 | 1\. 存在一个主配置文件 config.yaml，通过 defaults 列表组合不同的模块配置。2. 可以在命令行通过 python train.py model=new\_model trainer.lr=0.005 的方式覆盖默认参数。3. 支持通过 \--multirun 参数进行简单的超参数扫描。 |
| **R5** | **实验追踪** | **建立标准化的实验记录流程**每一次实验运行，都通过 W\&B 自动记录所有关键信息，形成可追溯、可对比的实验数据库。 | 1\. W\&B 的单次运行（Run）页面中，清晰记录了本次实验的所有超参数（来自 Hydra 配置）。2. 记录了关键指标（如 PSNR, SSIM）随训练进程的变化曲线。3. （可选）定期将模型输出的样本图像上传至 W\&B 进行可视化对比。 |
| **R6** | **脚本入口** | **设计单一、强大的任务分发入口**所有任务（如训练、评估、推理）共享同一个主脚本入口，通过命令行参数或配置来分发到不同的任务逻辑。 | 1\. python run.py task=train 和 python run.py task=evaluate 可以分别执行训练和评估任务。2. 所有任务共享同一套配置加载和环境初始化逻辑，避免代码重复。 |
| **R7** | **项目结构** | **采用符合 Python 打包标准的 src 布局**优化项目目录结构，将所有核心源码放置于一个独立的包内，实现源码与配置、脚本的分离。 | 1\. 项目根目录下存在 src/ (或与项目同名的包)，所有 import 均基于此包。2. 脚本和配置文件在根目录，可以清晰地调用源码包。3. 结构清晰，便于未来打包成可安装的 Python 包。 |
| **R8** | **扩展性** | **预留标准化的扩展接口**在设计数据增强、学习率调度、可视化等模块时，采用统一的接口或基类，方便未来添加新的实现。 | 1\. 数据增强模块接受一个 transform 列表配置，可以轻松组合 albumentations 或 torchvision 中的不同变换。2. 学习率调度器可以通过配置文件指定名称和参数来动态创建。 |

### **4\. 建议的最终项目结构**

```text
low-light-project/
│
├── .gitignore             # 告诉 Git 忽略哪些文件，比如 outputs/, data/, .vscode/
├── README.md              # ⭐ 项目的“说明书”，最重要！说明如何安装、运行、复现结果
├── requirements.txt       # 项目依赖库列表，通过 pip install -r requirements.txt 一键安装
├── pyproject.toml         # (推荐) 更现代化的项目配置和依赖管理方式，是未来的趋势
│
├── configs/               # 🎯 [核心] 所有实验的配置中心 (Hydra)
│   ├── config.yaml        # 主配置文件，定义默认组合和全局变量
│   ├── model/             # 模型相关配置
│   │   ├── unet_base.yaml
│   │   └── unet_attention.yaml
│   ├── dataset/           # 数据集相关配置
│   │   └── lol_dataset.yaml
│   └── trainer/           # 训练器相关配置
│       └── default_trainer.yaml
│
├── data/                  # 存放原始数据集的目录 (建议将此目录加入 .gitignore)
│   └── LOL/               # 示例：LOL 数据集
│       ├── our_low/
│       └── our_high/
│
├── notebooks/             # 💡 用于探索性分析和快速验证想法的 Jupyter Notebooks
│   ├── 1-explore_data.ipynb
│   └── 2-test_single_module.ipynb
│
├── outputs/               # 🤖 [自动生成] 所有实验的输出结果 (由 Hydra 管理)
│   └── 2025-07-28/
│       └── 12-30-00/
│           ├── .hydra/    # 本次运行的完整配置快照
│           ├── main.log   # 本次运行的日志文件
│           └── checkpoints/
│               └── last.pth
│
├── src/                   # 📦 [核心] 项目的所有 Python 源码包
│   └── llie/              # "llie" 是你项目的包名 (Low-Light Image Enhancement)
│       ├── __init__.py
│       │
│       ├── data/          # 数据加载模块 (PyTorch Datasets)
│       │   ├── __init__.py
│       │   └── lol_dataset.py
│       │
│       ├── engine/        # 🚀 核心训练与评估引擎
│       │   ├── __init__.py
│       │   └── trainer.py # 封装了完整的训练/验证循环的 Trainer 类
│       │
│       ├── models/        # 🧠 网络模型定义
│       │   ├── __init__.py
│       │   ├── attention.py
│       │   ├── blocks.py
│       │   └── architecture.py # 组装最终的网络结构
│       │
│       ├── losses.py      # 自定义损失函数
│       ├── metrics.py     # 评价指标 (PSNR, SSIM)
│       │
│       └── utils/         # 🛠️ 通用工具函数
│           ├── __init__.py
│           ├── logger.py    # Loguru 日志配置
│           ├── registry.py  # “即插即用”的注册器
│           └── visualizer.py# 结果可视化
│
├── train.py               # ▶️ [友好入口] 训练脚本
└── evaluate.py            # ▶️ [友好入口] 评估脚本


```

​    v1

```text
DMFourLLIE-Revised/
│
├── configs/               # 配置文件：模型结构、训练参数等
│   ├── default.yaml
│   ├── exp_baseline.yaml
│   └── exp_with_attention.yaml
│
├── models/                # 所有模型/模块文件（模块化改造）
│   ├── __init__.py
│   ├── base_model.py
│   ├── DMNet.py           # 主干模型
│   ├── attention.py       # 可选模块
│   └── denoise_branch.py
│
├── data/              # 数据加载
│   ├── __init__.py
│   └── llie_dataset.py
│
├── train.py               # 改为使用配置驱动
├── test.py
├── evaluate.py            # PSNR / SSIM / LPIPS等统一指标评估
│
├── utils/
│   ├── metrics.py
│   ├── logger.py
│   └── visualizer.py
│
├── scripts/               # 快速启动/多实验脚本
│   ├── run_all.sh
│   └── sweep_attention.sh
│
├── logs/                  # 日志输出
├── outputs/               # 模型保存 / 可视化结果
├── experiments/           # 记录每次实验配置和结果
│   ├── exp001/
│   └── exp002/
│
├── README.md
└── requirements.txt
```

