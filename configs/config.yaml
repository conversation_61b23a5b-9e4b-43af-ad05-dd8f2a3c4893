# LLIE (低光图像增强) 项目主配置文件
# 本文件使用 Hydra 框架组合不同的配置组件
#
# 使用方法：
# 1. 训练: python run.py
# 2. 评估: python run.py task=evaluate evaluation.checkpoint_path=path/to/model.pth
# 3. 推理: python run.py task=inference inference.checkpoint_path=path/to/model.pth inference.input_path=path/to/image.jpg

# @package _global_

# 默认配置组合 - 定义了实验的基本组件
defaults:
  - task: train                    # 任务类型：train/evaluate/inference
  - model: dmfourllie             # 模型架构：DMFourLLIE
  - dataset: LOLv2_Real           # 数据集：LOL数据集
  - trainer: default_trainer      # 训练器配置
  - _self_                        # 当前文件的配置优先级最高

# 实验名称 - 用于输出目录和日志标识
name: dmfourllie_experiment

# 全局设置
seed: 42                          # 随机种子，确保实验可复现
device: cuda                      # 计算设备：cuda/cpu
num_workers: 4                    # 数据加载的进程数

# 训练设置
use_validation: true              # 是否在训练过程中进行验证
resume_from: null                 # 恢复训练的检查点路径（.pth文件）

# 评估设置（当task=evaluate时使用）
evaluation:
  checkpoint_path: ???            # 模型检查点路径
  batch_size: 1                   # 评估批次大小
  save_images: true               # 是否保存增强后的图像
  save_metrics: true              # 是否保存详细的指标结果
  create_comparison: true         # 创建对比图像
  comparison_format: "grid"       # 对比图格式

# 推理设置（当task=inference时使用）
inference:
  checkpoint_path: ???            # 模型检查点路径
  input_path: ???                 # 输入图像或目录路径
  batch_size: 1                   # 推理批次大小
  save_format: "png"              # 输出图像格式
  create_comparison: true         # 创建对比图像

# 输出目录 - 由Hydra自动管理
# Hydra会自动创建带时间戳的目录
output_dir: ./outputs

# Weights & Biases 实验跟踪配置
wandb:
  project: low-light-enhancement  # W&B项目名称
  entity: null                    # W&B用户名或团队名（请填写您的用户名）
  tags:                          # 实验标签，便于分类和搜索
    - dmfourllie
    - low-light
    - enhancement
  mode: online                    # 模式：online/offline/disabled
  notes: "双阶段多分支傅里叶低光图像增强模型"

# 日志配置
logging:
  level: INFO                     # 日志级别：DEBUG/INFO/WARNING/ERROR
  use_wandb: true                 # 是否使用W&B记录日志

# 超参数扫描示例（配合 --multirun 使用）
# 使用方法：
# python run.py --multirun trainer.optimizer.lr=0.001,0.01,0.1
# python run.py --multirun model.architecture.s_nf=16,32,64 trainer.batch_size=8,16

# Hydra配置 - 自定义输出目录结构
hydra:
  run:
    dir: ./outputs/${task.task}/${now:%Y-%m-%d}/${now:%H-%M-%S}
  sweep:
    dir: ./outputs/multirun/${task.task}/${now:%Y-%m-%d}/${now:%H-%M-%S}
    subdir: ${hydra.job.num}
  job:
    name: ${task.task}_${now:%Y%m%d_%H%M%S}
    chdir: false
