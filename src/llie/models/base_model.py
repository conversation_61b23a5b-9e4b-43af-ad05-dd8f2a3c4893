"""
Base model classes providing common functionality.
"""

import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Union


class BaseArchitecture(nn.Module):
    """
    Base class for all model architectures.

    Provides common functionality like configuration management,
    parameter counting, and standard interfaces.
    """

    def __init__(self):
        super().__init__()

    def forward(self, *args, **kwargs):
        """Forward pass implementation. Should be overridden by subclasses."""
        raise NotImplementedError("Subclasses must implement forward()")

    def get_config(self) -> Dict[str, Any]:
        """Get model configuration for serialization. Should be overridden by subclasses."""
        return {"type": self.__class__.__name__}


class ModelEMA:
    """Exponential Moving Average for model parameters."""

    def __init__(self, model: nn.Module, decay: float = 0.9999, device: Optional[str] = None):
        self.model = model
        self.decay = decay
        self.device = device if device is not None else next(model.parameters()).device

        # Create EMA model
        self.ema_model = type(model)(model.config) if hasattr(model, "config") else model
        self.ema_model.to(self.device)
        self.ema_model.eval()

        # Initialize EMA parameters
        for ema_param, model_param in zip(self.ema_model.parameters(), model.parameters()):
            ema_param.data.copy_(model_param.data)

    def update(self, model: Optional[nn.Module] = None):
        """Update EMA parameters."""
        if model is not None:
            self.model = model

        with torch.no_grad():
            for ema_param, model_param in zip(self.ema_model.parameters(), self.model.parameters()):
                ema_param.data.mul_(self.decay).add_(model_param.data, alpha=1 - self.decay)

    def apply_shadow(self):
        """Apply EMA parameters to the original model for validation."""
        # Store original parameters
        self._backup = {}
        for name, param in self.model.named_parameters():
            self._backup[name] = param.data.clone()

        # Apply EMA parameters
        for (name, param), ema_param in zip(
            self.model.named_parameters(), self.ema_model.parameters()
        ):
            param.data.copy_(ema_param.data)

    def restore(self):
        """Restore original model parameters after validation."""
        if hasattr(self, "_backup"):
            for name, param in self.model.named_parameters():
                if name in self._backup:
                    param.data.copy_(self._backup[name])
            del self._backup

    @property
    def shadow(self):
        """Get EMA model state dict for checkpointing."""
        return self.ema_model.state_dict()


class GradientClipping:
    """Gradient clipping utility."""

    def __init__(self, max_norm: float = 1.0, norm_type: float = 2.0):
        self.max_norm = max_norm
        self.norm_type = norm_type

    def __call__(self, parameters):
        """Apply gradient clipping."""
        if isinstance(parameters, torch.Tensor):
            parameters = [parameters]
        return torch.nn.utils.clip_grad_norm_(parameters, self.max_norm, self.norm_type)
