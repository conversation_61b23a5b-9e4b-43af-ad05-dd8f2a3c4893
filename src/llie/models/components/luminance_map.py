"""
Luminance map processing components.

This module provides luminance map processing for low-light enhancement,
including encoder-decoder architecture with skip connections.
"""

import torch
import torch.nn as nn
from typing import List, Tuple
from ...utils.registry import register_model


class BasicConv(nn.Module):
    """
    Basic convolution block with optional normalization and activation.

    A flexible building block that can be configured for different use cases
    including transposed convolution for upsampling.

    Args:
        in_channels: Number of input channels.
        out_channels: Number of output channels.
        kernel_size: Size of the convolution kernel.
        stride: Convolution stride.
        padding: Convolution padding. If None, computed as kernel_size // 2.
        bias: Whether to use bias. Automatically disabled if norm=True.
        norm: Whether to use instance normalization.
        activation: Whether to use ReLU activation.
        transpose: Whether to use transposed convolution for upsampling.
    """

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        kernel_size: int = 3,
        stride: int = 1,
        padding: int = None,
        bias: bool = True,
        norm: bool = False,
        activation: bool = True,
        transpose: bool = False,
    ):
        super().__init__()

        # Disable bias if using normalization
        if bias and norm:
            bias = False

        # Compute padding if not specified
        if padding is None:
            if transpose:
                padding = kernel_size // 2 - 1
            else:
                padding = kernel_size // 2

        # Build layers
        layers = []

        if transpose:
            layers.append(
                nn.ConvTranspose2d(
                    in_channels,
                    out_channels,
                    kernel_size,
                    padding=padding,
                    stride=stride,
                    bias=bias,
                )
            )
        else:
            layers.append(
                nn.Conv2d(
                    in_channels,
                    out_channels,
                    kernel_size,
                    padding=padding,
                    stride=stride,
                    bias=bias,
                )
            )

        if norm:
            layers.append(nn.InstanceNorm2d(out_channels))

        if activation:
            layers.append(nn.ReLU(inplace=True))

        self.main = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the convolution block."""
        return self.main(x)


class DownScale(nn.Module):
    """
    Downsampling block using convolution with stride 2.

    Reduces spatial resolution while doubling the number of channels.
    """

    def __init__(self, in_channels: int):
        super().__init__()
        self.main = BasicConv(in_channels, in_channels * 2, 3, 2)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.main(x)


class UpScale(nn.Module):
    """
    Upsampling block using transposed convolution.

    Increases spatial resolution while halving the number of channels.
    """

    def __init__(self, in_channels: int):
        super().__init__()
        self.main = BasicConv(
            in_channels, in_channels // 2, kernel_size=4, stride=2, transpose=True
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.main(x)


@register_model("LuminanceMapProcessor")
class LuminanceMapProcessor(nn.Module):
    """
    Luminance map processing network using U-Net architecture.

    This network processes the luminance channel to generate enhancement maps
    that guide the low-light enhancement process. Uses an encoder-decoder
    architecture with skip connections for multi-scale feature processing.

    Args:
        depth: List of integers specifying the depth of processing at each scale.
               Default: [1, 1, 1, 1] for 4 scales.
        base_channels: Number of base channels. Doubled at each downsampling.
        input_channels: Number of input channels (usually 1 for luminance).
        output_channels: Number of output channels (usually 1 for enhancement map).

    Example:
        >>> processor = LuminanceMapProcessor(depth=[2, 2, 2, 2], base_channels=32)
        >>> luminance = torch.randn(4, 1, 256, 256)  # Batch of luminance images
        >>> enhancement_map = processor(luminance)   # Enhanced luminance map
    """

    def __init__(
        self,
        depth: List[int] = [1, 1, 1, 1],
        base_channels: int = 16,
        input_channels: int = 1,
        output_channels: int = 1,
    ):
        super().__init__()

        self.depth = depth
        self.base_channels = base_channels

        # Input projection
        self.conv_first = BasicConv(input_channels, base_channels, 3, 1)

        # Encoder: 4 scales with progressively more channels
        self.encoder = nn.ModuleList()
        current_channels = base_channels

        for i, d in enumerate(depth[:-1]):  # Exclude the last depth (bottleneck)
            # Feature processing at current scale
            self.encoder.append(BasicConv(current_channels, current_channels, 3, 1))
            self.encoder.append(
                nn.Sequential(
                    *[BasicConv(current_channels, current_channels, 3, 1) for _ in range(d)]
                )
            )

            # Downsampling
            self.encoder.append(DownScale(current_channels))
            current_channels *= 2

        # Bottleneck (middle processing)
        self.middle = nn.Sequential(
            *[BasicConv(current_channels, current_channels, 3, 1) for _ in range(depth[-1])]
        )

        # Decoder: 4 scales with progressively fewer channels
        self.decoder = nn.ModuleList()

        for i, d in enumerate(reversed(depth[:-1])):
            # Upsampling
            self.decoder.append(UpScale(current_channels))
            current_channels //= 2

            # Feature fusion (after concatenation with skip connection)
            # Note: channels doubled due to skip connection concatenation
            self.decoder.append(BasicConv(current_channels * 2, current_channels, 3, 1))

            # Feature processing at current scale
            self.decoder.append(
                nn.Sequential(
                    *[BasicConv(current_channels, current_channels, 3, 1) for _ in range(d)]
                )
            )

        # Output projection
        self.conv_last = nn.Conv2d(base_channels, output_channels, 3, 1, 1)

        # Initialize weights
        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize network weights using modern best practices."""
        for module in self.modules():
            if isinstance(module, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.InstanceNorm2d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)

    def encode(self, x: torch.Tensor) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        Encoder forward pass with skip connection collection.

        Args:
            x: Input tensor [B, C, H, W].

        Returns:
            Tuple of (encoded_features, skip_connections).
        """
        shortcuts = []

        for i in range(len(self.encoder)):
            x = self.encoder[i](x)

            # Collect skip connections before downsampling
            if (i + 2) % 3 == 0:  # After every [conv, blocks, downsample] group
                shortcuts.append(x)

        return x, shortcuts

    def decode(self, x: torch.Tensor, shortcuts: List[torch.Tensor]) -> torch.Tensor:
        """
        Decoder forward pass with skip connection fusion.

        Args:
            x: Encoded features [B, C, H, W].
            shortcuts: List of skip connection features.

        Returns:
            Decoded features [B, C, H, W].
        """
        for i in range(len(self.decoder)):
            # Fuse skip connections after upsampling
            if (i + 2) % 3 == 0:  # After every [upsample, fusion, blocks] group
                skip_index = len(shortcuts) - (i // 3 + 1)
                x = torch.cat([x, shortcuts[skip_index]], dim=1)

            x = self.decoder[i](x)

        return x

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the luminance map processor.

        Args:
            x: Input luminance image [B, 1, H, W].

        Returns:
            Enhanced luminance map [B, 1, H, W] with values in [0, 1].
        """
        # Input projection
        x = self.conv_first(x)

        # Encoder-decoder processing
        x, shortcuts = self.encode(x)
        x = self.middle(x)
        x = self.decode(x, shortcuts)

        # Output projection with sigmoid activation
        x = self.conv_last(x)
        y = torch.sigmoid(x)

        return y

    def get_config(self) -> dict:
        """Get model configuration for serialization."""
        return {
            "type": "LuminanceMapProcessor",
            "depth": self.depth,
            "base_channels": self.base_channels,
        }


# Legacy alias for backward compatibility
LuminaceMap = LuminanceMapProcessor
