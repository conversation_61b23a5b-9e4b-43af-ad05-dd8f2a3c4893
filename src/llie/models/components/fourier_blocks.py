"""
Fourier processing blocks for DMFourLLIE model.

This module contains the FFT-based processing components used in the first stage
of the DMFourLLIE architecture for amplitude and phase processing in frequency domain.
"""

from typing import Tuple, Optional
import torch
import torch.nn as nn
import torch.nn.functional as F
from loguru import logger

from ...utils import MODELS, register_model
from .common import SEBlock, make_layer


class FFTProcessBlock(nn.Module):
    """
    FFT-based processing block for frequency domain feature enhancement.

    This block processes features in the frequency domain using FFT, applies
    separate processing to amplitude and phase components, and incorporates
    cross-modal attention with infrared features and luminance maps.

    Args:
        channels: Number of feature channels.

    Example:
        >>> block = FFTProcessBlock(64)
        >>> x = torch.randn(4, 64, 32, 32)
        >>> fr = torch.randn(4, 64, 32, 32)  # Infrared features
        >>> y_map = torch.randn(4, 64, 32, 32)  # Luminance map
        >>> out, fr_out, y_out = block(x, fr, y_map)
    """

    def __init__(self, channels: int):
        super().__init__()
        self.channels = channels

        # Frequency domain preprocessing
        self.freq_preprocess = nn.Conv2d(channels, channels, 1, bias=True)

        # Amplitude and phase processing
        self.amp_processor = self._make_process_block(channels)
        self.phase_processor = self._make_process_block(channels)

        # Cross-modal feature processing
        self.fr_processor = self._make_process_block(channels)
        self.map_processor = self._make_process_block(channels)

        # Feature fusion
        self.feature_fusion = nn.Conv2d(channels * 2, channels, 1, bias=True)

        self._initialize_weights()

    def _make_process_block(self, channels: int) -> nn.Module:
        """Create a processing block for amplitude/phase/cross-modal features."""
        return nn.Sequential(
            nn.Conv2d(channels, channels, 1, bias=True),
            nn.LeakyReLU(0.1, inplace=True),
            nn.Conv2d(channels, channels, 1, bias=True),
        )

    def _initialize_weights(self):
        """Initialize weights using modern best practices."""
        for module in self.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="leaky_relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def _adjust_channels(self, x: torch.Tensor, target_channels: int) -> torch.Tensor:
        """
        Adjust tensor channels to match target channels.

        Args:
            x: Input tensor [B, C, H, W].
            target_channels: Target number of channels.

        Returns:
            Tensor with adjusted channels [B, target_channels, H, W].
        """
        B, C, H, W = x.shape

        if C == target_channels:
            return x
        elif C < target_channels:
            # Repeat channels if needed
            repeat_factor = target_channels // C + (1 if target_channels % C != 0 else 0)
            x = x.repeat(1, repeat_factor, 1, 1)[:, :target_channels, :, :]
        else:
            # Reduce channels using interpolation
            x = F.interpolate(x, size=(H, W), mode="bilinear", align_corners=False)
            x = x[:, :target_channels, :, :]

        return x

    def _cross_modal_attention(
        self, vis_features: torch.Tensor, ir_features: torch.Tensor
    ) -> torch.Tensor:
        """
        Apply cross-modal attention between visible and infrared features.

        Args:
            vis_features: Visible features [B, C, H, W].
            ir_features: Infrared features [B, C, H, W].

        Returns:
            Enhanced visible features [B, C, H, W].
        """
        # Normalize features to avoid numerical instability
        vis_norm = F.normalize(vis_features, dim=1)
        ir_norm = F.normalize(ir_features, dim=1)

        # Compute attention weights through element-wise multiplication
        B, C, H, W = vis_features.shape
        vis_flat = vis_norm.view(B, C, -1)
        ir_flat = ir_norm.view(B, C, -1)

        # Element-wise multiplication and softmax
        attention = torch.mul(vis_flat, ir_flat)
        attention = torch.softmax(attention, dim=2)
        attention = attention.view(B, C, H, W)

        # Apply attention with residual connection
        enhanced = vis_features * attention + vis_features
        return enhanced

    def forward(
        self, x: torch.Tensor, fr: torch.Tensor, y_map: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass through FFT processing block.

        Args:
            x: Input features [B, C, H, W].
            fr: Infrared features [B, C, H, W].
            y_map: Luminance map features [B, C, H, W].

        Returns:
            Tuple of (processed_features, processed_fr, processed_y_map).
        """
        _, _, H, W = x.shape

        # Preprocess input for frequency domain
        x_prep = self.freq_preprocess(x)

        # FFT and decomposition
        x_fft = torch.fft.rfft2(x_prep, norm="backward")
        amplitude = torch.abs(x_fft)
        phase = torch.angle(x_fft)

        # Process amplitude and phase separately
        amp_processed = self.amp_processor(amplitude)
        phase_processed = self.phase_processor(phase)

        # Process infrared features and apply cross-modal attention
        fr_processed = self.fr_processor(fr)
        phase_enhanced = self._cross_modal_attention(phase_processed, fr_processed)

        # Process luminance map and enhance amplitude
        y_map_processed = torch.sigmoid(self.map_processor(y_map))
        amp_enhanced = amp_processed * y_map_processed + amp_processed

        # Reconstruct complex signal
        real = amp_enhanced * torch.cos(phase_enhanced)
        imag = amp_enhanced * torch.sin(phase_enhanced)
        x_complex = torch.complex(real, imag)

        # Inverse FFT
        x_reconstructed = torch.fft.irfft2(x_complex, s=(H, W), norm="backward")

        # Residual connection
        output = x_reconstructed + x

        return output, fr_processed, y_map_processed


@register_model("FirstProcessModel")
class FirstProcessModel(nn.Module):
    """
    First processing stage using Fourier reconstruction.

    This model performs the first stage of DMFourLLIE processing, focusing on
    frequency domain enhancement through FFT-based blocks with skip connections
    and multi-scale feature fusion.

    Args:
        channels: Number of feature channels (default: 16).
        num_blocks: Number of FFT processing blocks (default: 6).
        input_channels: Number of input channels (default: 3).

    Example:
        >>> model = FirstProcessModel(channels=32, num_blocks=6)
        >>> x = torch.randn(4, 3, 256, 256)  # Input image
        >>> fr = torch.randn(4, 32, 256, 256)  # Infrared features
        >>> y_map = torch.randn(4, 32, 256, 256)  # Luminance map
        >>> out, y_out = model(x, fr, y_map)
    """

    def __init__(self, channels: int = 16, num_blocks: int = 6, input_channels: int = 3):
        super().__init__()

        self.channels = channels
        self.num_blocks = num_blocks

        # Initial feature extraction
        self.initial_conv = nn.Conv2d(input_channels, channels, 1, bias=True)

        # FFT processing blocks
        self.fft_blocks = nn.ModuleList([FFTProcessBlock(channels) for _ in range(num_blocks)])

        # Feature fusion layers for skip connections
        self.fusion_layers = nn.ModuleList(
            [
                nn.Sequential(nn.Conv2d(channels * 2, channels, 1, bias=True), SEBlock(channels))
                for _ in range(3)
            ]
        )

        # Final output projection
        self.output_conv = nn.Conv2d(channels, input_channels, 1, bias=True)

        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize weights using modern best practices."""
        for module in self.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="leaky_relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def forward(
        self, x: torch.Tensor, fr: torch.Tensor, y_map: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through first processing model.

        Args:
            x: Input image [B, 3, H, W].
            fr: Infrared features [B, C, H, W].
            y_map: Luminance map features [B, C, H, W].

        Returns:
            Tuple of (enhanced_image, processed_y_map).
        """
        # Initial feature extraction
        x0 = self.initial_conv(x)

        # First three FFT blocks with intermediate features
        x1, fr, y_map = self.fft_blocks[0](x0, fr, y_map)
        x2, fr, y_map = self.fft_blocks[1](x1, fr, y_map)
        x3, fr, y_map = self.fft_blocks[2](x2, fr, y_map)

        # Feature fusion with skip connections
        # Fuse x3 and x2
        x4_input = torch.cat([x3, x2], dim=1)
        x4_input = self.fusion_layers[0](x4_input)
        x4, fr, y_map = self.fft_blocks[3](x4_input, fr, y_map)

        # Fuse x4 and x1
        x5_input = torch.cat([x4, x1], dim=1)
        x5_input = self.fusion_layers[1](x5_input)
        x5, fr, y_map = self.fft_blocks[4](x5_input, fr, y_map)

        # Fuse x5 and x0
        x6_input = torch.cat([x5, x0], dim=1)
        x6_input = self.fusion_layers[2](x6_input)
        x6, fr, y_map = self.fft_blocks[5](x6_input, fr, y_map)

        # Final output projection
        output = self.output_conv(x6)

        return output, y_map

    def get_config(self) -> dict:
        """Get model configuration for serialization."""
        return {
            "type": "FirstProcessModel",
            "channels": self.channels,
            "num_blocks": self.num_blocks,
        }


class MultiConvBlock(nn.Module):
    """
    Multi-scale convolution block with grouped convolutions.

    This block applies convolutions with different kernel sizes to capture
    multi-scale features, uses grouped convolutions for efficiency, and
    includes squeeze-and-excitation attention.

    Args:
        channels: Number of input/output channels.
        num_scales: Number of different scales (default: 4).
        reduction_ratio: Channel reduction ratio (default: 4).

    Example:
        >>> block = MultiConvBlock(64, num_scales=4)
        >>> x = torch.randn(4, 64, 32, 32)
        >>> out = block(x)  # Same shape as input
    """

    def __init__(self, channels: int, num_scales: int = 4, reduction_ratio: int = 4):
        super().__init__()

        self.channels = channels
        self.num_scales = num_scales

        # Channel reduction for efficiency
        reduced_channels = channels // reduction_ratio
        self.conv_reduction = nn.Conv2d(channels, reduced_channels, 1, bias=True)
        self.activation = nn.LeakyReLU(0.1, inplace=True)

        # Multi-scale convolutions with different kernel sizes
        self.multi_scale_convs = nn.ModuleList(
            [
                nn.Conv2d(
                    reduced_channels,
                    reduced_channels,
                    kernel_size=3 + i * 2,  # 3x3, 5x5, 7x7, 9x9
                    padding=1 + i,
                    groups=reduced_channels,  # Grouped convolution
                    bias=True,
                )
                for i in range(num_scales)
            ]
        )

        # Feature fusion and channel restoration
        self.conv_fusion = nn.Conv2d(channels, channels, 1, bias=True)
        self.se_block = SEBlock(channels)

        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize weights using modern best practices."""
        for module in self.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="leaky_relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through multi-convolution block.

        Args:
            x: Input features [B, C, H, W].

        Returns:
            Enhanced features [B, C, H, W].
        """
        identity = x

        # Channel reduction
        x_reduced = self.activation(self.conv_reduction(x))

        # Multi-scale feature extraction
        multi_scale_features = []
        for conv in self.multi_scale_convs:
            # Apply multi-scale convolution
            x_scale = self.activation(conv(x_reduced))
            # Element-wise modulation
            x_scale = x_scale * torch.sigmoid(x_reduced)
            multi_scale_features.append(x_scale)

        # Concatenate and fuse multi-scale features
        x_concat = torch.cat(multi_scale_features, dim=1)
        x_fused = self.conv_fusion(x_concat)

        # Apply squeeze-and-excitation attention
        x_fused = self.se_block(x_fused)

        # Residual connection
        return identity + x_fused

    def get_config(self) -> dict:
        """Get block configuration for serialization."""
        return {
            "type": "MultiConvBlock",
            "channels": self.channels,
            "num_scales": self.num_scales,
        }
