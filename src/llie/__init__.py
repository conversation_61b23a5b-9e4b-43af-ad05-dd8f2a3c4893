"""
LLIE (Low-Light Image Enhancement) Package

A modern, modular framework for low-light image enhancement research.
Features:
- Registry pattern for plug-and-play components
- Hydra-based configuration management
- Comprehensive experiment tracking
- High code quality with type hints and documentation
"""

__version__ = "0.0.1"
__author__ = "OGAS"

from .utils.registry import MODELS, DATASETS, LOSSES, OPTIMIZERS, SCHEDULERS, METRICS

# Import all modules to trigger registrations
from . import losses, models, data, utils

__all__ = [
    "MODELS",
    "DATASETS",
    "LOSSES",
    "OPTIMIZERS",
    "SCHEDULERS",
    "METRICS",
]
