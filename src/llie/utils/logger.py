"""
Modern logging configuration using loguru.

Provides beautiful, structured logging with colors, automatic timestamps,
and flexible configuration. Replaces the traditional logging module with
a more user-friendly interface.

Examples:
    Basic usage:
    ```python
    from llie.utils.logger import get_logger

    logger = get_logger(__name__)
    logger.info("Training started")
    logger.success("Model saved successfully")
    logger.error("Failed to load checkpoint")
    ```

    With experiment tracking:
    ```python
    logger = get_logger(__name__, experiment_dir="./outputs/exp_001")
    ```
"""

import sys
from pathlib import Path
from typing import Optional, Union
from loguru import logger
import wandb


def setup_logger(
    name: str = "LLIE",
    level: str = "INFO",
    log_file: Optional[Union[str, Path]] = None,
    experiment_dir: Optional[Union[str, Path]] = None,
    use_wandb: bool = False,
) -> None:
    """
    Setup loguru logger with custom configuration.

    Args:
        name: Logger name/prefix for messages
        level: Logging level (DEBUG, INFO, WARNING, ERRO<PERSON>, CRITIC<PERSON>)
        log_file: Path to log file. If None, only console logging is used.
        experiment_dir: Experiment directory. Log file will be created as {experiment_dir}/train.log
        use_wandb: Whether to integrate with Weights & Biases logging

    Examples:
        >>> # Console only
        >>> setup_logger("Training")

        >>> # With file logging
        >>> setup_logger("Training", log_file="./logs/train.log")

        >>> # With experiment directory
        >>> setup_logger("Training", experiment_dir="./outputs/exp_001")
    """
    # Remove default logger
    logger.remove()

    # Console logger with colors and formatting
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
        "<level>{message}</level>",
        level=level,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )

    # File logger (if specified)
    if log_file is not None:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        logger.add(
            log_path,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=level,
            rotation="10 MB",  # Rotate when file gets too large
            retention="30 days",  # Keep logs for 30 days
            compression="zip",  # Compress rotated files
        )

    # Experiment directory logging
    if experiment_dir is not None:
        exp_path = Path(experiment_dir)
        exp_path.mkdir(parents=True, exist_ok=True)
        log_file = exp_path / "train.log"

        logger.add(
            log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=level,
            rotation="10 MB",
            retention="30 days",
            compression="zip",
        )

    # W&B integration (if enabled)
    if use_wandb:
        try:
            if wandb.run is not None:
                # Custom handler to send important logs to W&B
                class WandBHandler:
                    def write(self, record):
                        if record["level"].name in ["ERROR", "WARNING", "SUCCESS"]:
                            wandb.log({"log": record["message"]})

                logger.add(WandBHandler(), level="WARNING")
                logger.info("W&B logging integration enabled")
        except Exception as e:
            logger.warning(f"Failed to setup W&B logging: {e}")

    logger.info(f"Logger initialized with level: {level}")
    if log_file:
        logger.info(f"Logging to file: {log_file}")
    if experiment_dir:
        logger.info(f"Experiment directory: {experiment_dir}")


def get_logger(name: str):
    """
    Get a logger instance with the specified name.

    Args:
        name: Logger name (typically __name__)

    Returns:
        Configured logger instance

    Examples:
        >>> logger = get_logger(__name__)
        >>> logger.info("This is an info message")
        >>> logger.success("Operation completed successfully")
        >>> logger.warning("This is a warning")
        >>> logger.error("An error occurred")
    """
    # loguru uses a single global logger instance
    # We can patch it to include the name in context
    return logger.bind(name=name)


class LoggerContext:
    """
    Context manager for temporary logger configuration.

    Useful for changing log level or adding temporary handlers
    during specific operations.

    Examples:
        >>> with LoggerContext(level="DEBUG"):
        >>>     logger.debug("This debug message will be shown")
        >>> # Logger returns to previous configuration
    """

    def __init__(self, level: Optional[str] = None, **kwargs):
        self.level = level
        self.kwargs = kwargs
        self.handler_id = None

    def __enter__(self):
        if self.level:
            # Add temporary handler with different level
            self.handler_id = logger.add(
                sys.stderr,
                level=self.level,
                format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
                **self.kwargs,
            )
        return logger

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.handler_id:
            logger.remove(self.handler_id)


# Convenience functions for common logging patterns
def log_model_info(model, model_name: str = "Model"):
    """Log model architecture information."""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    logger.info(f"{model_name} Architecture:")
    logger.info(f"  Total parameters: {total_params:,}")
    logger.info(f"  Trainable parameters: {trainable_params:,}")
    logger.info(f"  Non-trainable parameters: {total_params - trainable_params:,}")


def log_experiment_start(config: dict, experiment_name: str):
    """Log experiment start with configuration."""
    logger.info("=" * 50)
    logger.info(f"🚀 Starting experiment: {experiment_name}")
    logger.info("=" * 50)

    # Log key configuration
    if "model" in config:
        logger.info(f"Model: {config['model'].get('type', 'Unknown')}")
    if "dataset" in config:
        logger.info(f"Dataset: {config['dataset'].get('type', 'Unknown')}")
    if "trainer" in config:
        trainer_cfg = config["trainer"]
        logger.info(f"Epochs: {trainer_cfg.get('max_epochs', 'Unknown')}")
        logger.info(f"Learning Rate: {trainer_cfg.get('lr', 'Unknown')}")
        logger.info(f"Batch Size: {trainer_cfg.get('batch_size', 'Unknown')}")


def log_experiment_end(results: dict):
    """Log experiment completion with results."""
    logger.info("=" * 50)
    logger.success("✅ Experiment completed successfully!")
    logger.info("=" * 50)

    # Log key results
    for metric, value in results.items():
        if isinstance(value, float):
            logger.info(f"{metric}: {value:.4f}")
        else:
            logger.info(f"{metric}: {value}")


__all__ = [
    "setup_logger",
    "get_logger",
    "LoggerContext",
    "log_model_info",
    "log_experiment_start",
    "log_experiment_end",
]
