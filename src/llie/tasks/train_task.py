"""
Training task implementation.
"""

from pathlib import Path
from typing import Dict, Any, Optional
import torch
from torch.utils.data import DataLoader
from omegaconf import DictConfig
from loguru import logger

from .base_task import BaseTask
from ..engine.trainer import ModernTrainer
from ..data.dataloader import create_dataloader
from ..utils.registry import DATASETS
from ..utils.experiment_manager import ExperimentManager
from . import evaluate_task


class TrainTask(BaseTask):
    """
    Training task for low-light image enhancement models.

    Handles model training with comprehensive logging, checkpointing,
    and experiment tracking.
    """

    def __init__(self, config: DictConfig):
        super().__init__(config)
        self.trainer = None
        self.train_loader = None
        self.val_loader = None

    def setup(self):
        """Setup training components."""
        logger.info("Setting up training task...")

        # Create data loaders
        self._setup_data_loaders()

        # Create trainer
        self.trainer = ModernTrainer(self.config)

        logger.success("Training task setup completed")

    def run(self) -> Dict[str, Any]:
        """
        Execute training task.

        Returns:
            Dictionary containing training results and metrics.
        """
        if self.trainer is None:
            self.setup()

        # Get training parameters
        num_epochs = self.config["trainer"]["max_epochs"]
        resume_from = self.config.get("resume_from")

        logger.info(f"Starting training for {num_epochs} epochs")
        if resume_from:
            logger.info(f"Resuming from: {resume_from}")

        # Start training
        self.trainer.train(
            train_loader=self.train_loader,
            val_loader=self.val_loader,
            num_epochs=num_epochs,
            resume_from=resume_from,
        )

        # Check if we should run evaluation after training
        test_after_train = (
            self.config.get("task", {}).get("training", {}).get("test_after_train", False)
        )
        evaluation_results = None

        if test_after_train:
            logger.info("🔍 Starting post-training evaluation...")
            evaluation_results = self._run_post_training_evaluation()

        # Return training summary
        training_results = {
            "task": "train",
            "epochs_completed": self.trainer.current_epoch,
            "steps_completed": self.trainer.current_step,
            "best_metrics": self.trainer.best_metrics,
            "final_lr": self.trainer._get_current_lr(),
        }

        if evaluation_results:
            training_results["evaluation"] = evaluation_results

        # 实验结果管理
        self._manage_experiment_results(evaluation_results)

        return training_results

    def _setup_data_loaders(self):
        """Setup training and validation data loaders."""
        from omegaconf import OmegaConf

        # Training loader - convert to dict to avoid struct restrictions
        dataset_dict = OmegaConf.to_container(self.config["dataset"], resolve=True)

        # Extract training-specific configuration
        train_split_config = dataset_dict.pop("train", {})
        train_config = OmegaConf.create(
            {**dataset_dict, **train_split_config, "split": "train", "is_train": True}
        )

        self.train_loader = create_dataloader(
            dataset_cfg=train_config,
            batch_size=self.config["trainer"]["batch_size"],
            num_workers=self.config.get("num_workers", 4),
            is_train=True,
        )

        logger.info(f"Training dataset: {len(self.train_loader.dataset)} samples")
        logger.info(f"Training batches: {len(self.train_loader)}")

        # Validation loader (if specified)
        if self.config.get("use_validation", True):
            # Extract validation-specific configuration
            dataset_dict_val = OmegaConf.to_container(self.config["dataset"], resolve=True)
            val_split_config = dataset_dict_val.pop("eval", {})
            val_config = OmegaConf.create(
                {**dataset_dict_val, **val_split_config, "split": "val", "is_train": False}
            )

            try:
                self.val_loader = create_dataloader(
                    dataset_cfg=val_config,
                    batch_size=self.config["trainer"].get("val_batch_size", 1),
                    num_workers=self.config.get("num_workers", 4),
                    is_train=False,
                )

                logger.info(f"Validation dataset: {len(self.val_loader.dataset)} samples")
                logger.info(f"Validation batches: {len(self.val_loader)}")

            except Exception as e:
                logger.warning(f"Failed to create validation loader: {e}")
                logger.warning("Training will proceed without validation")
                self.val_loader = None
        else:
            self.val_loader = None

    def _run_post_training_evaluation(self) -> Optional[Dict[str, Any]]:
        """
        运行训练后评估

        Returns:
            评估结果字典，如果评估失败则返回None
        """
        try:
            from omegaconf import OmegaConf

            # 构建评估配置 - 使用测试集配置
            dataset_config = OmegaConf.to_container(self.config["dataset"], resolve=True)
            eval_dataset_config = {
                "type": dataset_config["type"],
                "name": dataset_config["name"],
                "low_light_dir": dataset_config["eval"]["low_light_dir"],
                "normal_light_dir": dataset_config["eval"]["normal_light_dir"],
                "transforms": dataset_config["eval"]["transforms"],
                "image_extensions": dataset_config.get(
                    "image_extensions", [".png", ".jpg", ".jpeg"]
                ),
                "load_size": dataset_config.get("load_size"),
                "crop_size": dataset_config.get("crop_size"),
                "check_paired": dataset_config.get("check_paired", True),
                "allow_missing_pairs": dataset_config.get("allow_missing_pairs", False),
            }

            eval_config = OmegaConf.create(
                {
                    "device": self.config.get("device", "cuda"),
                    "model": self.config["model"],
                    "dataset": eval_dataset_config,
                    "checkpoint_path": str(self.trainer.output_dir / "models" / "final_model.pth"),  # 顶层checkpoint_path
                    "evaluation": {
                        "checkpoint_path": str(self.trainer.output_dir / "models" / "final_model.pth"),
                        "batch_size": 1,
                        "save_images": True,
                        "save_metrics": True,
                        "create_comparison": True,
                        "comparison_format": "grid",
                    },
                    "metrics": ["psnr", "ssim", "lpips", "mae"],
                    "output_dir": str(self.trainer.output_dir),  # 使用训练器的时间戳目录
                    "input_size": self.config.get("input_size", (3, 128, 128)),
                    "num_workers": 2,
                }
            )

            logger.info(f"Running evaluation with checkpoint: {eval_config.checkpoint_path}")

            # 运行评估
            evaluation_results = evaluate_task.run(eval_config)

            logger.success("✅ Post-training evaluation completed successfully")
            return evaluation_results

        except Exception as e:
            logger.error(f"Post-training evaluation failed: {e}")
            logger.warning("Training completed successfully, but evaluation failed")
            return None

    def _manage_experiment_results(self, evaluation_results: Optional[Dict[str, Any]]):
        """管理实验结果，使用新的日志和存储系统"""
        if evaluation_results is None:
            logger.warning("没有评估结果可用于实验管理")
            return

        try:
            # 初始化实验管理器
            experiment_manager = ExperimentManager()

            # 获取实验信息
            experiment_dir = Path(self.trainer.output_dir)
            metrics = evaluation_results.get("metrics", {})
            model_analysis = evaluation_results.get("model_analysis", {})
            detailed_results = evaluation_results.get("detailed_results", [])

            # 获取配置信息
            from omegaconf import OmegaConf

            config_dict = OmegaConf.to_container(self.config, resolve=True)

            # 保存实验结果并更新最优模型
            result_summary = experiment_manager.save_experiment_results(
                experiment_dir=experiment_dir,
                metrics=metrics,
                model_analysis=model_analysis,
                detailed_results=detailed_results,
                config=config_dict,
            )

            # 显示结果摘要
            logger.info(f"✅ 实验结果已保存，综合评分: {result_summary['comprehensive_score']:.2f}")

            # 显示更新的最优模型
            updated_models = result_summary["updated_best_models"]
            if any(updated_models.values()):
                updated_list = [metric for metric, updated in updated_models.items() if updated]
                logger.info(f"🎉 已更新最优模型: {', '.join(updated_list)}")
            else:
                logger.info("当前实验未刷新任何最优模型记录")

            # 清理实验文件（保留最终模型，删除中间检查点）
            experiment_manager.cleanup_experiment(
                experiment_dir, keep_final_model=True, keep_evaluation_images=False
            )

        except Exception as e:
            logger.error(f"实验管理失败: {e}")
            import traceback

            logger.debug(traceback.format_exc())

    def cleanup(self):
        """Cleanup resources after training."""
        if hasattr(self, "trainer") and self.trainer is not None:
            # Final cleanup
            torch.cuda.empty_cache()

        logger.info("Training task cleanup completed")
