"""
Evaluation task for Low-Light Image Enhancement.
"""

from omegaconf import DictConfig
from loguru import logger
import torch
from tqdm.rich import tqdm
import pandas as pd
from pathlib import Path
import numpy as np
from PIL import Image
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, MofNCompleteColumn
from rich.text import Text

from ..data.lol_dataset import LOLDataset
from ..utils.registry import MODELS, DATASETS
from ..utils.logging import ExperimentLogger
from ..utils.metrics import create_metric_tracker
from ..utils.model_analysis import analyze_model
from ..utils.storage import OutputManager


def run(cfg: DictConfig) -> None:
    """
    Run the evaluation task.

    Args:
        cfg: Hydra configuration for the evaluation task.
    """
    # 初始化Rich控制台
    console = Console()

    # 显示任务开始信息
    console.print(
        Panel.fit(
            "[bold blue]🚀 Low-Light Image Enhancement Evaluation[/bold blue]", border_style="blue"
        )
    )

    logger.info("🚀 Initializing Evaluation Task")

    # --- 1. Setup ---
    device = torch.device(cfg.device)

    # 设置输出目录 - 直接使用Hydra提供的任务分离路径
    from ..utils.storage import OutputManager
    from ..utils.storage.path_manager import PathManager

    try:
        from hydra.core.hydra_config import HydraConfig

        hydra_cfg = HydraConfig.get()
        experiment_dir = Path(hydra_cfg.runtime.output_dir)
        logger.info(f"使用Hydra评估输出目录: {experiment_dir}")

        # 创建标准目录结构
        path_manager = PathManager(experiment_dir.parent.parent)
        path_manager._create_standard_subdirs(experiment_dir, "evaluate")

        output_dir = experiment_dir / "evaluation"

        # 设置实验日志记录器
        experiment_name = experiment_dir.name
        exp_logger = ExperimentLogger(experiment_name, experiment_dir)

        # 记录实验开始并保存配置文件
        from omegaconf import OmegaConf
        config_dict = OmegaConf.to_container(cfg, resolve=True)
        exp_logger.log_experiment_start(config_dict)

        # 创建OutputManager
        output_manager = OutputManager(str(experiment_dir.parent.parent), task_type="evaluate")

        # 为评估任务创建模型软链接
        checkpoint_path = cfg.evaluation.get("checkpoint_path")
        if checkpoint_path and Path(checkpoint_path).exists():
            models_dir = experiment_dir / "models"
            output_manager.create_model_symlink(checkpoint_path, models_dir, "evaluated_model.pth")
            logger.info(f"为评估任务创建模型软链接: {checkpoint_path}")

    except Exception as e:
        logger.warning(f"Hydra配置获取失败: {e}")
        # 回退到手动创建目录
        output_manager = OutputManager("./outputs", task_type="evaluate")
        experiment_dir = output_manager.create_experiment_directory("evaluate")
        output_dir = experiment_dir / "evaluation"

        # 设置实验日志记录器
        experiment_name = experiment_dir.name
        exp_logger = ExperimentLogger(experiment_name, experiment_dir)

        # 记录实验开始并保存配置文件
        from omegaconf import OmegaConf
        config_dict = OmegaConf.to_container(cfg, resolve=True)
        exp_logger.log_experiment_start(config_dict)

        # 为新的评估目录创建模型软链接
        checkpoint_path = cfg.evaluation.get("checkpoint_path")
        if checkpoint_path and Path(checkpoint_path).exists():
            models_dir = experiment_dir / "models"
            output_manager.create_model_symlink(checkpoint_path, models_dir, "evaluated_model.pth")
            logger.info(f"为评估任务创建模型软链接: {checkpoint_path}")

    output_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"评估结果将保存到: {output_dir}")

    save_images = cfg.get("save_images", True)
    # 注意：不再创建单独的images目录，因为OutputManager会生成comparison_grid.jpg

    # --- 2. Build Model ---
    logger.info("Building model...")
    from omegaconf import OmegaConf

    model_cfg = OmegaConf.to_container(cfg.model, resolve=True)
    model = MODELS.build(model_cfg)
    model.to(device)

    # --- 3. Load Checkpoint ---
    checkpoint_path = cfg.get("checkpoint_path") or cfg.get("evaluation", {}).get("checkpoint_path")

    # 如果没有指定checkpoint_path，尝试使用最新的模型
    if not checkpoint_path:
        latest_model_path = Path("./outputs/models/latest_checkpoint.pth")
        if latest_model_path.exists():
            checkpoint_path = str(latest_model_path)
            logger.info(f"自动使用最新模型: {checkpoint_path}")
        else:
            logger.warning("未找到最新模型文件，将使用随机权重")

    # 为评估任务创建模型软链接（如果找到了模型）
    if checkpoint_path and Path(checkpoint_path).exists():
        models_dir = experiment_dir / "models"
        output_manager.create_model_symlink(checkpoint_path, models_dir, "evaluated_model.pth")
        logger.info(f"为评估任务创建模型软链接: {checkpoint_path}")

    if checkpoint_path:
        logger.info(f"Loading checkpoint from: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)
        if "model_state_dict" in checkpoint:
            model.load_state_dict(checkpoint["model_state_dict"])
        else:
            model.load_state_dict(checkpoint)
    else:
        logger.warning("No checkpoint path provided, using random weights")

    model.eval()

    # --- 4. Build DataLoader ---
    logger.info("Building data loader...")
    from torch.utils.data import DataLoader

    # Create dataset - 使用测试数据配置
    dataset_cfg = OmegaConf.to_container(cfg.dataset, resolve=True)

    # 确保使用测试数据配置
    if "test" in dataset_cfg:
        test_cfg = dataset_cfg["test"]
        # 将测试配置合并到主配置中
        dataset_cfg.update(test_cfg)
    elif "eval" in dataset_cfg:
        # 如果没有test配置，使用eval配置
        eval_cfg = dataset_cfg["eval"]
        dataset_cfg.update(eval_cfg)

    dataset = DATASETS.build(dataset_cfg)
    val_loader = DataLoader(
        dataset,
        batch_size=cfg.get("batch_size", 1),
        shuffle=False,
        num_workers=cfg.get("num_workers", 4),
        pin_memory=True,
    )

    # --- 5. Setup Metrics ---
    metric_names = cfg.get("metrics", ["psnr", "ssim", "mae", "lpips"])
    metric_tracker = create_metric_tracker(metric_names, device)

    # --- 5.1. Model Complexity Analysis ---
    console.print("\n[bold yellow]📊 Analyzing Model Complexity...[/bold yellow]")
    input_size = cfg.get("input_size", (3, 256, 256))
    model_analysis = analyze_model(model, input_size=input_size, print_summary=False)

    # 创建模型复杂度分析表格
    complexity_table = Table(
        title="🧠 Model Complexity Analysis", show_header=True, header_style="bold magenta"
    )
    complexity_table.add_column("Metric", style="cyan", no_wrap=True)
    complexity_table.add_column("Value", style="green")
    complexity_table.add_column("Details", style="yellow")

    params = model_analysis["parameters"]
    flops = model_analysis["flops"]
    timing = model_analysis["timing"]

    complexity_table.add_row(
        "Parameters", f"{params['total_parameters']:,}", f"{params['model_size_mb']:.2f} MB"
    )
    complexity_table.add_row("FLOPs", flops["flops_formatted"], "Forward pass computation")
    complexity_table.add_row(
        "Inference Speed", f"{timing['mean_time'] * 1000:.2f} ms", f"{timing['fps']:.2f} FPS"
    )

    console.print(complexity_table)

    # --- 6. Run Evaluation ---
    console.print("\n[bold green]🔍 Starting Evaluation Process...[/bold green]")
    results = []
    enhanced_image_paths = []  # 收集增强图片路径
    low_light_image_paths = []  # 收集原始低光图片路径
    metric_names = cfg.get("metrics", ["psnr", "ssim", "lpips", "mae"])
    metric_tracker.reset()

    # 创建Rich进度条
    with Progress(
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        MofNCompleteColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        eval_task = progress.add_task("🔍 Evaluating Images", total=len(val_loader))

        with torch.no_grad():
            for i, batch in enumerate(val_loader):
                inputs = batch["low_light"].to(device)
                targets = batch["normal_light"].to(device)

                # 获取图片名称和路径信息
                if "metadata" in batch:
                    metadata = batch["metadata"]
                    img_names = [Path(path).stem for path in metadata["low_light_path"]]
                    batch_low_paths = [Path(path) for path in metadata["low_light_path"]]
                else:
                    img_names = [f"img_{i:04d}_{j:02d}" for j in range(inputs.shape[0])]
                    batch_low_paths = [None] * inputs.shape[0]

                # Forward pass
                outputs = model(inputs)

                # Update metrics
                metric_tracker.update(outputs, targets)

                # Save individual results
                for j in range(outputs.shape[0]):
                    enhanced = outputs[j]
                    gt = targets[j]
                    name = img_names[j]
                    low_path = batch_low_paths[j]

                    # Calculate individual metrics
                    individual_metrics = {}
                    for metric_name in metric_names:
                        metric_fn = metric_tracker.metrics[metric_name]
                        metric_value = metric_fn(enhanced.unsqueeze(0), gt.unsqueeze(0)).item()
                        individual_metrics[metric_name] = metric_value

                    results.append({"name": name, **individual_metrics})

                    # 收集图片信息用于生成对比图片（不再保存单独的增强图片）
                    if save_images:
                        # 将增强图片转换为PIL格式并临时保存用于对比图生成
                        enhanced_np = enhanced.cpu().numpy().transpose(1, 2, 0)
                        enhanced_np = np.clip(enhanced_np * 255, 0, 255).astype(np.uint8)
                        enhanced_pil = Image.fromarray(enhanced_np)

                        # 创建临时路径用于对比图生成
                        temp_enhanced_path = output_dir / f"temp_{name}.png"
                        enhanced_pil.save(temp_enhanced_path)

                        # 收集路径信息用于生成对比图片
                        enhanced_image_paths.append(temp_enhanced_path)
                        if low_path:
                            low_light_image_paths.append(low_path)

                # 更新进度条
                progress.update(eval_task, advance=1)

    # --- 7. Save Results ---
    df = pd.DataFrame(results)

    # Calculate average metrics
    avg_metrics = metric_tracker.compute()

    # 创建美观的结果表格
    results_table = Table(
        title="📊 Evaluation Results", show_header=True, header_style="bold green"
    )
    results_table.add_column("Metric", style="cyan", no_wrap=True)
    results_table.add_column("Average Value", style="green", justify="right")
    results_table.add_column("Performance", style="yellow")

    # 定义性能评级函数
    def get_performance_rating(metric_name, value):
        if metric_name.lower() == "psnr":
            if value >= 25:
                return "🟢 Excellent"
            elif value >= 20:
                return "🟡 Good"
            elif value >= 15:
                return "🟠 Fair"
            else:
                return "🔴 Poor"
        elif metric_name.lower() == "ssim":
            if value >= 0.9:
                return "🟢 Excellent"
            elif value >= 0.8:
                return "🟡 Good"
            elif value >= 0.7:
                return "🟠 Fair"
            else:
                return "🔴 Poor"
        elif metric_name.lower() in ["mae", "mse"]:
            if value <= 0.05:
                return "🟢 Excellent"
            elif value <= 0.1:
                return "🟡 Good"
            elif value <= 0.2:
                return "🟠 Fair"
            else:
                return "🔴 Poor"
        elif metric_name.lower() == "lpips":
            if value <= 0.1:
                return "🟢 Excellent"
            elif value <= 0.2:
                return "🟡 Good"
            elif value <= 0.3:
                return "🟠 Fair"
            else:
                return "🔴 Poor"
        else:
            return "📊 N/A"

    for metric_name, value in avg_metrics.items():
        performance = get_performance_rating(metric_name, value)
        results_table.add_row(metric_name.upper(), f"{value:.4f}", performance)

    console.print("\n")
    console.print(results_table)

    # 使用OutputManager保存结果并生成对比图片
    logger.info("使用OutputManager保存评估结果...")

    # 准备配置信息
    config_dict = {
        "model": cfg.get("model", {}),
        "dataset": cfg.get("dataset", {}),
        "evaluation": {
            "checkpoint_path": cfg.evaluation.get("checkpoint_path"),
            "metrics": metric_names,
            "batch_size": cfg.evaluation.get("batch_size", 1),
        },
    }

    # 使用OutputManager保存结果（包括生成对比图片）
    # 评估任务不更新最佳模型，只有训练任务才更新
    updated_metrics = output_manager.save_experiment_results(
        experiment_dir=experiment_dir,
        metrics=avg_metrics,
        model_analysis=model_analysis,
        detailed_results=results,
        config=config_dict,
        image_files=enhanced_image_paths if enhanced_image_paths else None,
        low_light_images=low_light_image_paths if low_light_image_paths else None,
        max_image_samples=6,  # 最多显示6张对比图片
        update_best_models=False,  # 评估任务不更新最佳模型
    )

    # 清理临时文件
    if save_images and enhanced_image_paths:
        for temp_path in enhanced_image_paths:
            if temp_path.name.startswith("temp_") and temp_path.exists():
                temp_path.unlink()
                logger.debug(f"已清理临时文件: {temp_path}")

    logger.success("评估结果已保存，包括对比图片生成")

    # Print final summary
    logger.success("📊 Final Evaluation Summary:")
    logger.info(f"  PSNR: {avg_metrics.get('psnr', 0):.4f}")
    logger.info(f"  SSIM: {avg_metrics.get('ssim', 0):.4f}")
    logger.info(f"  LPIPS: {avg_metrics.get('lpips', 0):.4f}")
    logger.info(f"  MAE: {avg_metrics.get('mae', 0):.4f}")
    logger.info(f"  Parameters: {model_analysis['parameters']['total_parameters']:,}")
    logger.info(f"  FLOPS: {model_analysis['flops']['flops_formatted']}")
    logger.info(f"  Inference Time: {model_analysis['timing']['mean_time'] * 1000:.2f} ms")

    # 显示完成信息面板
    completion_panel = Panel.fit(
        f"[bold green]✅ Evaluation Completed Successfully![/bold green]\n\n"
        f"📁 Results saved to: [cyan]{output_dir}[/cyan]\n"
        f"📊 Processed {len(results)} images\n"
        f"🎯 Best PSNR: {max([r.get('psnr', 0) for r in results]):.4f}\n"
        f"🎯 Best SSIM: {max([r.get('ssim', 0) for r in results]):.4f}",
        border_style="green",
        title="🎉 Task Complete",
    )
    console.print("\n")
    console.print(completion_panel)

    logger.info("✅ Evaluation task completed.")

    # Return results for potential use by calling code
    return {"metrics": avg_metrics, "model_analysis": model_analysis, "output_dir": str(output_dir)}
